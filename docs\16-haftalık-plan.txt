16 Haftalık Geliştirme Planı
� 16 Haftalık Çalışma Planı
� Faz 1: Temel Hazırlık ve Prototipleme (Hafta 1–4)
✅ 1. Hafta – Gereksinim & Teknik Planlama
• <PERSON>r<PERSON>n sahi<PERSON>yle toplantı, bekle<PERSON><PERSON><PERSON> toplanması
• Teknik yığın seçimnin geliştiricilerle birlikte kararlaştırılması (backend, frontend, AI,
DB, auth)
• Sistem diyagramları (component, data, auth)
• Proje yönetim araçlarının kurulumu
✅ 2. Hafta – Dergi İçeriği Ayrıştırma (Parser)
• PDF/HTML/Markdown parse işlemi
• Sayfa, başl<PERSON>k, yazar gibi meta bilgilerin ayrıştırılması
• JSON veri çıktısı hazırlanması ✅
3. Hafta – Embedding & RAG
• Embedding süreci (OpenAI, BGE-small vs.)
• Qdrant kurulumu ve veri yükleme
✅• Benzer içerik bulma fonksiyonları
4. <PERSON>fta – Basit ChatBot Prototipi
• Gradio veya Streamlit arayüzü
• Prompt şablonu oluşturma
• Soru → Retriever → Generator → Cevap döngüsü
�✅ Faz 2: Uygulama Geliştirme (Hafta 5–10)
5. Hafta – Admin Panel Başlangıcı
• JWT tabanlı kullanıcı yönetimi
• Dergi yükleme ve listeleme sayfası
✅ • Yetkilendirme rolleri
6. Hafta – Embedding Otomasyonu
• Yüklenen içeriklerde otomatik embedding işlemi
• Başarısız embedding logları
• Queue sistemi (Celery/Worker)
✅ 7. Hafta – AI API Geliştirme
• API katmanı hazırlanması
• Prompt sistemine göre rol ayrımı
• Çoklu dil desteği başlangıcı (TR & EN)
✅ 8. Hafta – React UI Başlangıcı
• Temel chat arayüzü
• Mesaj listesi, giriş ekranı
• Google OAuth2 ile kullanıcı girişi
✅ 9. Hafta – QR Kod ile Chat Başlatma
• Dergi sayfalarına QR üretimi
• QR ile AI kontekst başlatma
• UI’da QR girişi desteği
✅ 10. Hafta – Sesli Okuma (TTS)
• Coqui TTS veya Piper kurulumu
• Yanıtların ses dosyasına çevrilmesi
• UI’a “Dinle” butonu eklenmesi
� Faz 3: Ürünleştirme ve Gelişmiş Özellikler (Hafta 11–16) ✅
11. Hafta – Kişiselleştirme Altyapısı
• Kullanıcı davranışı takibi
• İlgi alanı çıkarımı
✅ • Otomatik özet/newsletter üretimi
12. Hafta – Raporlama ve İzleme
• Elastic APM / Datadog kurulumu
• Kullanım istatistikleri (oturum süresi, sorgu sayısı)
✅ • Admin panelde temel dashboard
13. Hafta – Gelişmiş Admin Panel
• İçerik geçmişi yönetimi
• Embedding durumu ve hata raporları
✅ • Kullanıcı geri bildirimleri
14. Hafta – Mobil Uyumluluk ve Sesli Chat
• UI responsive tasarım
• Sesli soru (WebSpeech API veya Whisper)
• TTS ile cevap okunması (tam çift yönlü sesli chat)
✅ 15. Hafta – Production Ortam Kurulumu
• Dockerfile / Azure DevOps Deployment Süreçleri
• Production Hazırlıkları
• Rate limiting, güvenlik testleri
✅ 16. Hafta – Test & Lansman Hazırlığı
• Kabul testleri
• Demo ve son kullanıcı testi
• Lansman dökümanı ve eğitim materyalleri
� Ek Bileşenler
Ürün Kapsamında Değerlendirilmeyen Eklenebilecek Özellikler (v2)
• Kişisel reklam entegrasyonu
• Kullanıcı temelli embedding (advanced RAG)
• WebSocket ile canlı mesajlaşma
Bu plan, düzenli haftalık sprintlerle uygulanabilecek şekilde tasarlanmıştır. Her haftanın
sonunda minimum bir test edilebilir çıktı sunmak hedeflenir.
n Özetler
Modül Hedef
� Dergi Yükleme & Parse PDF/HTML’den sayfa & metin çıkarımı, JSON’a dönüştürme
� Embedding + RAG Vektör DB (Qdrant) + AI cevabı üretme
� Chatbot Arayüzü Soru sorma + streaming cevap + sesli oynat
� Admin Panel İçerik yükleme, embedding durumu, kullanıcı listesi
� OAuth2 Google girişi ile kullanıcı doğrulama
� APM & Log Elastic APM veya Datadog ile izleme ve hata takibi
� TTS Türkçe ve İngilizce yanıtları dinletme (Coqui REST API ile)
� QR Entegrasyonu QR ile kontekste dayalı chat başlatma
� Test + Deploy Test senaryoları, Docker deploy, dökümantasyon