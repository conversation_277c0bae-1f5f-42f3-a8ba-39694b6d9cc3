# NiooAI - Yapay Zeka Destekli Dergi Chatbot Sistemi

## 1. <PERSON><PERSON>, e-dergi içerikleri için AI destekli bir chatbot sistemi istiyor. Kullanıcılar doğal dilde sorular sorarak dergi içeriklerinden bilgi alabilecek. Sistem, firmanın sağladığı dergileri RAG ile işleyecek ve GPT-4o API'sini (firmanın API key'i ile) kullanacak. Gelecekte kişiselleştirme, reklam ve sesli etkileşim eklenecek.

## 2. Hede<PERSON>

- Dergi okuyucuları: <PERSON>çerik sorgulama, özet alma.
- Editörler/Admin: İçerik yükleme, yönetim.

## 3. Ana Özellikler

### 3.1. Dergi Yükleme ve Parse

- PDF/HTML/Markdown formatlarında dergi yükleme.
- <PERSON><PERSON> <PERSON>ril<PERSON> (ba<PERSON><PERSON><PERSON><PERSON>, yazar, say<PERSON>) <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, JSON'a dönüştürme.

### 3.2. RAG Tabanlı AI

- Embedding: OpenAI text-embedding-3-small ile firmanın dergilerini vektörleme.
- Vektör DB: Qdrant.
- Retriever: Kullanıcı sorusuna göre benzer içerikler çekme.
- Generator: GPT-4o (firmanın API key'i ile) cevap üretimi.
- Prompt Engineering: Rol bazlı (okuyucu, editör).

### 3.3. Chatbot UI

- React tabanlı, mobil uyumlu.
- Google OAuth2 ile giriş.
- Streaming mesajlar, sohbet geçmişi.
- QR ile sayfa spesifik chat başlatma.

### 3.4. Admin Panel

- Kullanıcı yönetimi (JWT).
- Dergi yükleme, embedding durumu izleme.
- Raporlama: Kullanım istatistikleri.

### 3.5. Ek Özellikler

- TTS: Sesli okuma (Coqui/Piper).
- Kişiselleştirme: Kullanıcı davranışına göre öneriler, newsletter.
- Güvenlik: Rate limiting, OAuth2.

## 4. Teknoloji Yığını

- UI: React
- Backend: .NET
- AI: GPT-4o (firmanın API key'i) + Qdrant
- DB: MongoDB + Redis
- Auth: Google OAuth2
- DevOps: Azure DevOps, Docker

## 5. Geliştirme Planı

16 haftalık planı temel al: Faz 1 (Prototip), Faz 2 (Uygulama), Faz 3 (Ürünleştirme).

## 6. Teslimatlar

- Çalışan demo
- Dökümantasyon (API, kullanım kılavuzu)
- Kaynak kodu (GitHub)
