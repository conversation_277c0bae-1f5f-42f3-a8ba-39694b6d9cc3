using Backend.Models;

namespace Backend.Interfaces
{
    public interface IUserRepository
    {
        // Create operations
        Task<User> CreateUserAsync(User user);
        
        // Read operations
        Task<User?> GetUserByIdAsync(string id);
        Task<User?> GetUserByEmailAsync(string email);
        Task<User?> GetUserByGoogleIdAsync(string googleId);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);
        Task<bool> EmailExistsAsync(string email);
        
        // Update operations
        Task<bool> UpdateUserAsync(string id, User user);
        Task<bool> UpdateLastLoginAsync(string id);
        Task<bool> VerifyEmailAsync(string id);
        Task<bool> ChangePasswordAsync(string id, string hashedPassword);
        
        // Delete operations
        Task<bool> DeleteUserAsync(string id);
        Task<bool> DeactivateUserAsync(string id);
        
        // Authentication helpers
        Task<User?> ValidateUserCredentialsAsync(string email, string hashedPassword);
    }
} 