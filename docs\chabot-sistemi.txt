NiooAI İçin Yapay Zeka Destekli ChatBot Sistemi – Teknik Rapor
1. Proje Amacı
Firmaya özel geliştirilecek yapay zeka destekli bir sistemle, dergi içerikleri üzerinden
kullanıcıların doğal dilde sorular sorarak bilgi alabileceği; ileride kişiselleştirilmiş reklam,
newsletter ve sesli etkileşim gibi özelliklerle zenginleştirilebilecek bir platform
sunulacaktır.
2. Sistem Bileşenleri
2.1. Admin Panel
• Dergi içeriklerinin PDF/HTML/Markdown formatlarında yüklenmesi
• İçerik başlık, yazar, tarih gibi meta verilerle ilişkilendirme
• Kullanıcı yönetimi (rol bazlı yetkilendirme)
• AI sistemini yöneten ayarlar (token limiti, model seçimi, vs.)
2.2. AI Agent (RAG Tabanlı)
• Embedding Engine: OpenAI / HuggingFace tabanlı model (örn: text-embedding-3-
small)
• Vektör Veri Tabanı: Qdrant / Weaviate / Pinecone (ölçeklenebilir yapı)
• Retriever: Similarity-based content fetcher (kullanıcının sorusuna uygun içerikleri
çeker)
• Generator: GPT-4o mini modeli ile cevap üretimi
• Prompt Engineering: Sistem mesajları, kullanıcı rolüne göre yönlendirme (editör,
okuyucu, vs.)
2.3. Backend (API & Yönetim Servisleri)
• RESTful API (veya gRPC) ile ChatBot, Admin Panel ve diğer sistemlerle bağlantı
• Google OAuth2 login / kullanıcı profil servisi
• AI servisleri (embedding, rag, search, feedback)
• APM (Application Performance Monitoring) Entegrasyonu (Elastic APM / Datadog /
New Relic)
• Günlükler (Loglama): Kullanıcı istekleri, hata kayıtları, sistemsel durumlar
• Raporlama API’si: Kullanıcı bazlı analiz, popüler konular, kullanım oranları
2.4. ChatBot UI (Web + Mobil uyumlu)
• Reaktif arayüz
• Koyu / Açık tema desteği
• Google ile giriş
• Anlık mesajlaşma + geçmiş sohbetler
• AI mesajlarının streaming (parça parça) sunulması
• Sayfa bazlı QR okuma ile AI kontekst başlatma
3. Gelecek Geliştirmeler
3.1. Sesli Chat Özelliği
• Web Speech API / Whisper model entegrasyonu
• TTS (Text-to-Speech): Sesli okuma desteği
• Konuşma geçmişi takibi
3.2. Kişiselleştirme Katmanı
• Okuyucunun ilgisini çeken içeriklere göre öneriler
• Kullanıcı temelli embedding ile özel RAG
• Otomatik newsletter üretimi (haftalık özet, ilgi alanı bazlı)
• Kişiselleştirilmiş reklam modülü
3.3. QR Kodu ile AI Kontekst Entegrasyonu
• Dergi sayfalarına özel QR üretimi
• QR üzerinden spesifik konuya özel chat başlatma (örn: “Sayfa 14 - Yapay Zeka yazısı
hakkında ne düşünüyorsun?”)
4. Teknik Gereksinimler
Sunucu Altyapısı
• GPU destekli sunucu (NVIDIA A100 / RTX 4090 önerilir) (LOCAL Modeller İçin)
• Vektör veritabanı için SSD NVMe disk (Qdrant kullanılacak)
• Docker Compose / Kubernetes tabanlı dağıtım
• APM + Log server (Elastic Stack veya Datadog)
Güvenlik
• OAuth2 + JWT tabanlı kimlik doğrulama
• Rate limiting & abuse protection
• XSS / CSRF / SQL Injection önlemleri
5. Kullanım Raporlama
• Toplam kullanıcı sayısı, oturum süresi, popüler sorgular
• AI performans skoru (feedback üzerinden)
• En çok okunan/yönlendirilen içerikler
• Sistem yükü ve yanıt süreleri istatistikleri
6. Teknoloji Yığını
Katman Teknoloji
UI React
Backend .NET
AI GPT-4o mini + Qdrant
Auth Google OAuth2
APM Elastic APM
DB Cassandra / MongoDB + Redis (cache)
DevOps Azure DevOps
7. Teslimatlar
• Çalışan demo
• Teknik dökümantasyon (kurulum, yapılandırma)
• API dokümantasyonu (Swagger / Redoc)
• Kullanıcı kullanım kılavuzu
• Yönetim paneli erişimi