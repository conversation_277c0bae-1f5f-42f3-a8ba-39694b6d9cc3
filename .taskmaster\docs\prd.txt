<context>
# Overview  
NiooAI, dergi sahiplerinin e-dergi içerikleri için geliştirdiği AI destekli chatbot sistemidir. Kullanıcılar doğal dilde sorular sorarak içeriklerden bilgi alır. Problem: Okuyucuların dergi içeriklerine hızlı erişim ve etkileşim eksikliği. Hedef kitle: Dergi okuyucuları ve editörler. Değer: Kişiselleştirilmiş, etkileş<PERSON>li içerik keşfi sağlar.

# Core Features  
- Dergi Yükleme ve Parse: PDF/HTML'den metin/meta çıkarma, JSON dönüştürme. Önemli çünkü içerikler AI için hazırlanmalı.
- RAG Tabanlı AI: Embedding ile vektörleme (OpenAI), Qdrant DB, GPT-4o (firmanın API key'i) ile cevap üretimi. Önemli çünkü doğru, kontekstli yanıtlar verir.
- Chatbot UI: React arayüz, streaming mesajlar, QR entegrasyonu. Önemli çünkü kullanıcı dostu etkileşim.
- Admin Panel: Kullanıcı/dergi yönetimi, raporlama. Önemli çünkü operasyonel kontrol.
- Ek: TTS, kişiselleştirme. Yüksek seviyede: API çağrıları ile entegre.

# User Experience  
- Personalar: Okuyucu (sorgu yapan), Editör (yöneten).
- Akışlar: Giriş → Dergi seç/soru sor → Yanıt al → TTS dinle.
- UI/UX: Mobil uyumlu, koyu/açık tema, anlık feedback.
</context>
<PRD>
# Technical Architecture  
- Bileşenler: Frontend (React), Backend (.NET API), AI (GPT-4o + Qdrant), DB (MongoDB + Redis).
- Veri Modelleri: Dergi (meta, içerik), Kullanıcı (profil, tarihçe).
- API'ler: RESTful, Google OAuth2, OpenAI entegrasyonu.
- Altyapı: Docker, Azure DevOps, GPU sunucu.

# Development Roadmap  
- MVP: Temel parse, embedding, basit chatbot UI, admin yükleme.
- Gelecek: TTS, kişiselleştirme, QR, raporlama, sesli chat.

# Logical Dependency Chain
- Temel: Backend API ve DB kurulumu.
- Sonra: Parse ve embedding (içerik işleme).
- UI: Hızlı görünür frontend (chat arayüzü) için temel bileşenler.
- Genişletme: Her özellik atomic, üzerine inşa edilebilir (örn: temel chat → streaming ekle).

# Risks and Mitigations  
- Teknik: API entegrasyon hataları → Test-driven development.
- MVP Belirleme: Minimum özelliklerle başla, iteratif genişlet.
- Kaynak: Task-master ile önceliklendirme.

# Appendix  
- Araştırma: 16-haftalık plan ve chabot-sistemi.txt'den türetilmiş.
- Spesifikasyonlar: Rate limiting, güvenlik önlemleri.
</PRD>