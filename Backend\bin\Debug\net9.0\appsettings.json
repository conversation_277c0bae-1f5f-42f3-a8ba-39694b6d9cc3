{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"MongoDB": "mongodb://localhost:27017/niooai_database", "Redis": "localhost:6379"}, "DatabaseSettings": {"ConnectionString": "mongodb://localhost:27017/niooai_database", "DatabaseName": "niooai_database", "UsersCollectionName": "users", "MagazinesCollectionName": "magazines", "ChatSessionsCollectionName": "chat_sessions"}, "OpenAI": {"ApiKey": "", "Model": "gpt-4o", "EmbeddingModel": "text-embedding-3-small"}, "Qdrant": {"Url": "http://localhost:6333", "CollectionName": "magazine_embeddings"}, "Authentication": {"Google": {"ClientId": "", "ClientSecret": ""}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-here-change-in-production", "Issuer": "NiooAI", "Audience": "NiooAI-Users", "ExpirationHours": 24}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001"]}}