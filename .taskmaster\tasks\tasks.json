{"master": {"tasks": [{"id": 1, "title": "Setup Backend (.NET API) Project", "description": "Set up the core .NET API project structure, including necessary dependencies for database access (MongoDB, Redis), external API integrations (OpenAI, Google OAuth2), and hosting environment (Docker).", "details": "Create a new .NET Core Web API project. Configure project settings, add NuGet packages for MongoDB driver, StackExchange.Redis, HttpClient, Microsoft.AspNetCore.Authentication.Google. Set up basic middleware and routing. Prepare Dockerfile for containerization.", "testStrategy": "Verify project builds successfully. Run basic endpoint tests (e.g., a health check) in a local environment and within a Docker container.", "priority": "high", "dependencies": [], "status": "done", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table(s) to store user information, including credentials and profile data.", "dependencies": [], "details": "The `users` table should include columns for `id` (primary key), `username` (unique), `email` (unique), `password_hash` (string), `created_at`, and `updated_at`. Use a database migration tool (e.g., Alembic, Flyway) to apply the schema changes.\n<info added on 2025-07-14T08:20:05.665Z>\nMongoDB schema design and initialization script created. The `scripts/mongo-init.js` file defines the `users`, `magazines`, and `chat_sessions` collections with validation schemas and indexes. An admin user is automatically created during initialization.\n</info added on 2025-07-14T08:20:05.665Z>", "status": "done", "testStrategy": "Verify the migration runs successfully. Manually inspect the database schema to confirm all columns and constraints (unique, not null) are correctly created."}, {"id": 2, "title": "Create User Registration API Endpoint", "description": "Develop a public API endpoint (e.g., POST /api/users/register) that allows new users to create an account.", "dependencies": [1], "details": "The endpoint should accept `username`, `email`, and `password`. It must validate the input (e.g., password strength, valid email format), check for existing username/email, hash the password using a strong algorithm like bcrypt, and save the new user to the database.\n<info added on 2025-07-14T08:21:19.969Z>\nAuthController created and Register endpoint defined. Register endpoint prepared as a placeholder in Backend/Controllers/AuthController.cs with TODOs for MongoDB integration and password hashing.\n</info added on 2025-07-14T08:21:19.969Z>", "status": "done", "testStrategy": "Write unit tests to cover success cases (valid registration), failure cases (duplicate email/username, invalid input), and edge cases. Test that the password stored in the database is a hash, not plaintext."}, {"id": 3, "title": "Implement User Login and JWT Issuance", "description": "Create an API endpoint (e.g., POST /api/auth/login) for users to authenticate and receive an access token.", "dependencies": [1], "details": "The endpoint should accept `email` and `password`. It will find the user by email, verify the provided password against the stored hash. On success, generate a JSON Web Token (JWT) containing the user ID and an expiration date. The JWT secret key should be stored securely as an environment variable.", "status": "done", "testStrategy": "Unit test the login logic for successful authentication (returns a valid JWT) and failed authentication (invalid credentials, user not found). Write a separate test to verify the JWT payload and signature."}, {"id": 4, "title": "Develop Authentication Middleware for Protected Routes", "description": "Create a middleware that intercepts requests to protected endpoints, validates the JWT from the Authorization header, and attaches the authenticated user's data to the request object.", "dependencies": [3], "details": "The middleware should extract the token from the 'Authorization: Bearer <token>' header. It must verify the token's signature and check for expiration. If valid, decode the payload to get the user ID, fetch the user from the database, and attach it to the request context. If invalid, it should return a 401 Unauthorized error.", "status": "done", "testStrategy": "Test the middleware with a valid token, an expired token, a token with an invalid signature, and a missing token. Ensure it correctly passes the request for valid tokens and blocks it for invalid ones."}, {"id": 5, "title": "Create a Protected 'Get User Profile' Endpoint", "description": "Implement a sample protected endpoint (e.g., GET /api/users/me) that uses the authentication middleware to return the current logged-in user's profile information.", "dependencies": [2, 4], "details": "This endpoint will be protected by the middleware from subtask 4. It should access the user object attached to the request by the middleware and return non-sensitive user data (e.g., `id`, `username`, `email`). Ensure the password hash is NOT returned.", "status": "done", "testStrategy": "Write an integration test: register a user, log in to get a JWT, then use that JWT to call this endpoint and assert that the correct user data is returned. Test that calling without a valid token returns a 401 error."}]}, {"id": 2, "title": "Setup Database (MongoDB, Redis) Integration", "description": "Configure and integrate MongoDB for persistent data storage (Magazine metadata, User profiles, Chat history) and Redis for caching and potentially session management.", "details": "Set up connection strings and configuration for MongoDB and Redis in the .NET application. Implement data context or repository patterns for initial data models (Magazine, User). Ensure connections are established correctly on application startup.", "testStrategy": "Write unit tests for database connection and basic CRUD operations on dummy models. Verify data can be written to and read from both MongoDB and Redis instances.", "priority": "high", "dependencies": [1], "status": "in-progress", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the database schema and migration scripts for storing user data, including credentials and profile information.", "dependencies": [], "details": "Define a 'users' table with columns: 'id' (UUID, primary key), 'email' (VARCHAR, unique), 'hashed_password' (VARCHAR), 'created_at' (TIMESTAMP), and 'updated_at' (TIMESTAMP). Use a database migration tool like Alembic or Flyway to manage schema versions.", "status": "done", "testStrategy": "Run the migration script against a test database. Verify that the 'users' table and all specified columns with their constraints are created correctly by inspecting the schema directly."}, {"id": 2, "title": "Develop API Endpoints for User Registration and Login", "description": "Implement the backend API endpoints for user registration (/api/auth/register) and login (/api/auth/login).", "dependencies": [1], "details": "The registration endpoint should validate input, hash the password using bcrypt, and store the new user. The login endpoint should verify credentials and, on success, generate a signed JWT containing the user ID and an expiration claim. Implement robust error handling for cases like duplicate emails or incorrect passwords.", "status": "in-progress", "testStrategy": "Write integration tests for both endpoints. Test successful registration and login flows. Add tests for failure cases, such as submitting invalid data, registering with an existing email, and attempting to log in with incorrect credentials. Assert that a valid JWT is returned on successful login."}, {"id": 3, "title": "Create Authentication Middleware for Protected Routes", "description": "Implement middleware to verify the JWT on incoming requests to secure specific API routes.", "dependencies": [2], "details": "The middleware should extract the JWT from the 'Authorization: Bearer <token>' header. It must validate the token's signature and check for expiration. If the token is valid, decode the payload to identify the user and attach the user object to the request context. If the token is invalid or missing, the middleware should return a 401 Unauthorized response.", "status": "pending", "testStrategy": "Create a test-only protected endpoint. Write integration tests that call this endpoint with a valid token, an expired token, a malformed token, and no token. Assert that the request succeeds only with the valid token and returns a 401 status for all other cases."}]}, {"id": 3, "title": "Implement Google OAuth2 Authentication", "description": "Implement user authentication using Google OAuth2 for secure access to the application.", "details": "Configure Google OAuth2 in the .NET backend using Microsoft.AspNetCore.Authentication.Google. Set up callback endpoints and token handling. Implement user creation/lookup logic in the database upon successful authentication.", "testStrategy": "Test the Google OAuth2 flow end-to-end. Verify users can initiate login, authenticate with Google, and are correctly redirected and identified in the backend. Check user data is stored/updated in the database.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables and migrations to store user information, including credentials and profile data.", "dependencies": [], "details": "Define a 'users' table with columns for id, username (unique), email (unique), password_hash, created_at, and updated_at. Use a database migration tool to create and apply the schema. Ensure password_hash column is of sufficient length for bcrypt hashes.", "status": "pending", "testStrategy": "Run the migration against a test database. Verify the table and all columns are created with the correct data types and constraints. Write a unit test to ensure the user model can be instantiated and saved."}, {"id": 2, "title": "Develop API Endpoints for User Registration and Login", "description": "Create the backend API endpoints for handling user registration and authentication, including password hashing and JWT generation.", "dependencies": [1], "details": "Implement a POST /api/register endpoint that validates input, hashes the password, and creates a new user record. Implement a POST /api/login endpoint that validates credentials, and on success, returns a JSON Web Token (JWT). Implement robust error handling for duplicate users or invalid credentials.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. Test the success path for registration and login. Test failure paths, such as submitting duplicate emails, invalid passwords, or incorrect login details. Assert that a valid JWT is returned on successful login."}, {"id": 3, "title": "Build Frontend Registration and Login Forms", "description": "Create the user interface components for the registration and login pages that interact with the backend API.", "dependencies": [2], "details": "Using a frontend framework (e.g., React, Vue), build two forms. Implement client-side validation for fields like email format and password strength. On submit, make asynchronous requests to the API endpoints. On successful login, store the JWT in local storage and redirect the user to a protected area of the application. Display appropriate error messages from the API.", "status": "pending", "testStrategy": "Use a component testing library to test form validation logic. Conduct end-to-end tests using a tool like Cypress to simulate a user registering, logging out, and logging back in. Verify that API calls are made correctly and that the JWT is handled properly."}]}, {"id": 4, "title": "Integrate OpenAI Embedding API", "description": "Integrate the OpenAI API into the backend for generating text embeddings from processed magazine content.", "details": "Add HttpClient logic to make requests to the OpenAI Embedding API (e.g., `text-embedding-ada-002`). Securely manage the OpenAI API key (e.g., using environment variables or Azure Key Vault). Implement a service class for embedding generation.", "testStrategy": "Write integration tests to call the OpenAI API with sample text and verify that valid embedding vectors are returned. Ensure API key handling is secure.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table to store user information, including credentials and profile data. This forms the foundation for all user-related features.", "dependencies": [], "details": "Define a 'users' table with columns for 'id' (PK), 'email' (unique), 'username' (unique), 'password_hash' (string), 'created_at', and 'updated_at'. Use a migration tool to apply the schema. Create a corresponding User model in the application's ORM.", "status": "pending", "testStrategy": "Write unit tests for the User model to ensure data validation and relationships work as expected. Manually verify the database schema after running the migration."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/register) that allows new users to create an account.", "dependencies": [1], "details": "The endpoint should accept an email, username, and password. Implement input validation for all fields (e.g., email format, password strength). Hash the password using bcrypt before storing it in the database. Return a 201 status code on success.", "status": "pending", "testStrategy": "Write integration tests to cover successful registration, registration with a duplicate email/username, and registration with invalid input (e.g., weak password, invalid email)."}, {"id": 3, "title": "Implement User Login and JWT Generation", "description": "Create an API endpoint (e.g., POST /api/login) for users to authenticate and receive a JSON Web Token (JWT).", "dependencies": [1], "details": "The endpoint should accept user credentials (email/password). Verify the credentials against the stored password hash. Upon successful authentication, generate a signed JWT containing the user ID and an expiration claim. Return the JWT in the response body.", "status": "pending", "testStrategy": "Write integration tests for successful login with correct credentials, failed login with incorrect credentials, and login attempts for non-existent users. Unit test the JWT generation and signing logic in isolation."}, {"id": 4, "title": "Create Middleware for Protected Routes", "description": "Implement authentication middleware to protect specific API endpoints, ensuring they are only accessible by authenticated users.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization' header. It must validate the token's signature and check for expiration. If valid, decode the payload to identify the user and attach the user object to the request context. If invalid, return a 401 Unauthorized error.", "status": "pending", "testStrategy": "Create a sample protected endpoint. Write integration tests to verify that requests with a valid JWT are allowed, while requests with an invalid, expired, or missing token are rejected with a 401 status."}]}, {"id": 5, "title": "Integrate Qdrant Vector Database", "description": "Integrate the Qdrant vector database client into the backend for storing and querying content embeddings.", "details": "Add the Qdrant .NET client library. Configure connection to the Qdrant instance. Implement a service class for creating collections, inserting vectors with associated metadata (e.g., chunk ID, magazine ID), and performing similarity searches.", "testStrategy": "Write integration tests to connect to Qdrant, create a test collection, insert sample vectors, and perform a basic similarity search to ensure correct results are returned.", "priority": "high", "dependencies": [1], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Create User Database Schema", "description": "Define and implement the database schema required for storing user information, including credentials and profile data.", "dependencies": [], "details": "Create a 'users' table with columns: 'id' (UUID, primary key), 'email' (VARCHAR, unique), 'password_hash' (VARCHAR), 'created_at' (TIMESTAMP), and 'updated_at' (TIMESTAMP). Use a migration tool like Alembic or Flyway to version control the schema.", "status": "pending", "testStrategy": "Run the migration script against a test database. Verify that the 'users' table and all specified columns are created with the correct data types and constraints. Write a test to ensure the migration can be applied and reverted successfully."}, {"id": 2, "title": "Develop API Endpoints for User Registration and Login", "description": "Implement the backend API endpoints for creating a new user account and authenticating an existing user.", "dependencies": [1], "details": "Create a POST '/api/v1/register' endpoint to handle new user creation, ensuring password hashing (e.g., with bcrypt). Create a POST '/api/v1/login' endpoint that validates credentials and returns a JSON Web Token (JWT) upon success.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. For registration, test success cases, duplicate email errors, and invalid input validation. For login, test with valid credentials (expecting a JWT), invalid credentials (expecting a 401 Unauthorized error), and non-existent users."}, {"id": 3, "title": "Build Frontend Registration and Login UI Components", "description": "Create the user interface forms for registration and login, and connect them to the backend API endpoints.", "dependencies": [2], "details": "Develop two React components: 'RegistrationForm' and 'LoginForm'. Implement state management for form inputs and client-side validation. On form submission, make an API call to the corresponding endpoint. On successful login, store the received JWT in local storage and redirect the user to the dashboard.", "status": "pending", "testStrategy": "Use a component testing library like Jest/RTL to test form rendering and validation logic. Conduct end-to-end tests using Cypress or Playwright to simulate the full user flow: filling out the registration form, submitting, and then using those credentials to log in successfully."}]}, {"id": 6, "title": "Implement Magazine Upload API Endpoint", "description": "Create backend API endpoints for uploading magazine files (PDF/HTML) and storing initial metadata in MongoDB.", "details": "Implement a RESTful API endpoint (e.g., POST /api/magazines/upload) that accepts file uploads. Validate file types (PDF/HTML). Store magazine metadata (title, author, upload date, file path/reference) in the MongoDB database. Handle file storage securely (e.g., on disk or blob storage).", "testStrategy": "Use API testing tools (e.g., Postman, Swagger UI) to test file uploads. Verify correct metadata is stored in MongoDB and the file is saved successfully. Test with valid and invalid file types.", "priority": "high", "dependencies": [1, 2], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table(s) to store user information, including credentials and profile data.", "dependencies": [], "details": "Define a 'users' table with columns: 'id' (UUID, primary key), 'email' (varchar, unique, not null), 'password_hash' (varchar, not null), 'created_at' (timestamp), and 'updated_at' (timestamp). Use a database migration tool like Alembic or Flyway to script and apply the schema changes.", "status": "pending", "testStrategy": "Run the migration and manually inspect the database schema to confirm the 'users' table and all specified columns, constraints, and data types have been created correctly. No application-level tests are needed for this subtask."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/v1/users/register) to allow new users to create an account.", "dependencies": [1], "details": "The endpoint should accept an email and password. It must validate the input (e.g., valid email format, password meets complexity requirements). Use a strong hashing algorithm like bcrypt to hash the password before storing the new user record in the database. Return a 201 Created status on success.", "status": "pending", "testStrategy": "Write unit tests for input validation logic. Write an integration test that sends a valid registration payload and asserts that a new user is created in the database with a properly hashed password. Test for error cases like duplicate emails."}, {"id": 3, "title": "Implement Login Endpoint and JWT Generation", "description": "Create an API endpoint (e.g., POST /api/v1/auth/login) for users to authenticate and receive a JSON Web Token (JWT).", "dependencies": [1], "details": "The endpoint will accept an email and password. It will retrieve the user by email, verify the provided password against the stored hash using bcrypt's compare function, and if successful, generate a signed JWT. The JWT payload should include the user ID and an expiration claim. Return the JWT in the response body.", "status": "pending", "testStrategy": "Write integration tests for successful login (assert a valid JWT is returned) and failed login attempts (wrong password, user not found), ensuring correct HTTP status codes (200, 401) are returned. Unit test the JWT generation logic separately."}, {"id": 4, "title": "Create Authentication Middleware for Protected Routes", "description": "Implement middleware to secure API endpoints, requiring a valid JWT for access.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization: Bearer <token>' header. It must verify the token's signature and check for expiration. If the token is valid, decode it and attach the user's identity to the request context. If the token is missing or invalid, the middleware must abort the request and return a 401 Unauthorized response.", "status": "pending", "testStrategy": "Create a sample protected endpoint. Write integration tests to access this endpoint with: a) a valid token (expect 200 OK), b) an invalid/expired token (expect 401 Unauthorized), and c) no token (expect 401 Unauthorized)."}]}, {"id": 7, "title": "Implement PDF/HTML Parsing Logic", "description": "Develop backend logic to parse text content and extract metadata from uploaded PDF and HTML files.", "details": "Use appropriate libraries for PDF parsing (e.g., PdfPig, iText7) and HTML parsing (e.g., HtmlAgilityPack). Implement logic to read content, clean it (remove headers, footers, irrelevant tags), and extract key metadata if available within the document.", "testStrategy": "Create unit tests with sample PDF and HTML files to verify text content is correctly extracted and cleaned. Test metadata extraction with files containing relevant tags/fields.", "priority": "high", "dependencies": [6], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables to store user information, including credentials, profile data, and session tokens.", "dependencies": [], "details": "The 'users' table should include columns for user_id (primary key), username (unique), email (unique), hashed_password, salt, created_at, and updated_at. Consider adding a 'roles' table and a 'user_roles' join table for role-based access control (RBAC).", "status": "pending", "testStrategy": "Verify the schema by running migration scripts against a test database. Manually inspect the created tables and constraints to ensure they match the design. Write unit tests for the data access layer to confirm basic CRUD operations on the user table."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/users/register) that allows new users to create an account.", "dependencies": [1], "details": "The endpoint must validate input (e.g., check for existing username/email, enforce password complexity). Hash the password using a strong algorithm like bcrypt or Argon2 before storing it in the database. Return a success message and user data (without the password) or appropriate error codes.", "status": "pending", "testStrategy": "Write integration tests to cover success cases (valid registration), failure cases (duplicate email/username, invalid password format, missing fields), and check the database to ensure the user is created correctly with a hashed password."}, {"id": 3, "title": "Develop User Login API Endpoint", "description": "Create an API endpoint (e.g., POST /api/auth/login) to authenticate users and issue an access token.", "dependencies": [1], "details": "The endpoint should accept user credentials (email/username and password). It will retrieve the user from the database, compare the provided password with the stored hash, and if they match, generate a JSON Web Token (JWT). The JWT payload should contain user_id and role.", "status": "pending", "testStrategy": "Integration tests should cover successful login with correct credentials, failed login with incorrect credentials (wrong password, non-existent user), and verify the structure and signature of the returned JWT."}, {"id": 4, "title": "Create Authentication Middleware for Protected Routes", "description": "Develop middleware to verify the JWT on incoming requests to secure specific API endpoints.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization' header. It must validate the token's signature and expiration. If valid, decode the payload and attach the user information to the request object. If invalid, it should return a 401 Unauthorized error.", "status": "pending", "testStrategy": "Write integration tests for a sample protected endpoint. Test cases should include requests with a valid token, an expired token, a malformed token, and no token at all, ensuring the correct HTTP status codes are returned."}, {"id": 5, "title": "Implement 'Forgot Password' and Reset Flow", "description": "Create the endpoints and logic for a secure password reset process.", "dependencies": [1, 2], "details": "Create a 'POST /api/auth/forgot-password' endpoint that generates a unique, short-lived reset token and sends a reset link to the user's email. Create a 'POST /api/auth/reset-password' endpoint that accepts the token and a new password, validates the token, and updates the user's password in the database.", "status": "pending", "testStrategy": "End-to-end testing of the full flow: request a reset, use the token to reset the password (mocking the email service), and then try to log in with the new password. Test edge cases like using an expired or invalid token."}]}, {"id": 8, "title": "Implement Text Chunking and Embedding", "description": "Implement backend logic to chunk the parsed text content into smaller pieces suitable for embedding and generate vector embeddings using the integrated OpenAI API.", "details": "Develop text chunking strategy (e.g., fixed size with overlap). Iterate through text chunks, call the OpenAI Embedding service (Task 4) for each chunk, and associate the resulting vector with the chunk text and its source magazine/page.", "testStrategy": "Write unit tests to verify text is correctly chunked according to the defined strategy. Write integration tests to process chunks through the embedding service and confirm valid vectors are generated for each chunk.", "priority": "high", "dependencies": [7, 4], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables and fields to store user account information, including credentials and profile data.", "dependencies": [], "details": "Define a 'users' table with columns: 'id' (UUID, primary key), 'email' (varchar, unique), 'password_hash' (varchar), 'full_name' (varchar), 'created_at' (timestamp), and 'updated_at' (timestamp). Use a migration tool like Alembic or Flyway to script and apply the schema changes.", "status": "pending", "testStrategy": "Run the migration and verify the table structure and constraints in a test database. Write a unit test for the User model to ensure it maps correctly to the schema and can be instantiated."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/v1/users/register) that allows a new user to sign up for an account.", "dependencies": [1], "details": "The endpoint should accept an email, password, and full name. Implement input validation for email format and password strength. Hash the password using bcrypt before storing it in the database. On success, return a 201 Created status and the new user's ID and email.", "status": "pending", "testStrategy": "Write integration tests to cover: successful registration, registration with a duplicate email (should fail), registration with an invalid email format, and registration with a weak password. Verify that the password stored in the database is a valid hash and not plaintext."}, {"id": 3, "title": "Implement User Login and JWT Generation Endpoint", "description": "Create an API endpoint (e.g., POST /api/v1/auth/login) to authenticate users and issue a JSON Web Token (JWT).", "dependencies": [1], "details": "The endpoint should accept an email and password. It must find the user by email, then use bcrypt to compare the provided password with the stored hash. If they match, generate a signed JWT containing the user's ID and an expiration claim (e.g., 1 hour). Return the JWT in the response body.", "status": "pending", "testStrategy": "Write integration tests for: successful login with valid credentials (verifying a valid JWT is returned), login with an incorrect password, and login with a non-existent email. Add a helper function to decode the JWT in tests to assert its payload is correct."}]}, {"id": 9, "title": "Store Embeddings in Qdrant", "description": "Implement backend logic to store the generated text embeddings and their associated metadata in the Qdrant vector database.", "details": "Use the Qdrant client (Task 5) to insert the vectors generated in Task 8 into the appropriate Qdrant collection. Store metadata alongside vectors, such as the original text chunk, magazine ID, page number, etc., to enable retrieval and context reconstruction.", "testStrategy": "Write integration tests to insert a batch of generated vectors into Qdrant. Verify that the vectors and their metadata are correctly stored and can be retrieved by ID.", "priority": "high", "dependencies": [8, 5], "status": "pending", "subtasks": [{"id": 1, "title": "Develop Backend API Endpoint for User Authentication", "description": "Create a secure API endpoint that accepts user credentials (email and password), validates them against the database, and returns a JSON Web Token (JWT) on success.", "dependencies": [], "details": "Use Node.js/Express to create a POST endpoint at `/api/v1/auth/login`. Implement password hashing comparison using bcrypt. On successful validation, generate a signed JWT containing the user ID and role, with a 24-hour expiration. Return appropriate HTTP status codes for success (200), bad request (400), and unauthorized (401).", "status": "pending", "testStrategy": "Write integration tests using Supertest to cover successful login, incorrect password, and non-existent user scenarios. Verify status codes and the structure of the returned JWT payload."}, {"id": 2, "title": "Create Frontend Login UI Component", "description": "Build a responsive and accessible login form component using a modern frontend framework like React or Vue.", "dependencies": [], "details": "The component should include input fields for email and password, a submit button, and a section for displaying error messages. Implement client-side validation to check for empty fields and valid email format before submission. Manage form state locally within the component.", "status": "pending", "testStrategy": "Use Storybook to visually test the component in various states (default, loading, error). Write unit tests with Jest and React Testing Library to verify form input handling and validation logic."}, {"id": 3, "title": "Integrate Frontend Login Form with Backend API", "description": "Connect the frontend login component to the backend authentication endpoint, handle the API response, and manage the application's authentication state.", "dependencies": [1, 2], "details": "On form submission, use a library like Axios to send the user's credentials to the `/api/v1/auth/login` endpoint. If the request is successful, store the received JWT securely (e.g., in an HttpOnly cookie). Update the global application state (e.g., using Redux or Context API) to reflect the logged-in status and redirect the user to their dashboard. Display any server-side error messages on the form.", "status": "pending", "testStrategy": "Perform end-to-end testing with Cypress. Create a test that fills out the login form with valid credentials, submits it, and verifies that the user is redirected to the dashboard and the JWT is present. Create another test for a failed login attempt to ensure an error message is displayed correctly."}]}, {"id": 10, "title": "Implement Chat API (RAG Logic)", "description": "Implement the core Chat API endpoint in the backend, handling user queries, retrieving relevant context using RAG, and generating responses using GPT-4o.", "details": "Create a RESTful API endpoint (e.g., POST /api/chat). Receive user query. Generate embedding for the query using OpenAI (Task 4). Perform a similarity search in Qdrant (Task 5) to retrieve relevant text chunks (Task 9). Construct a prompt for GPT-4o including the user query and retrieved context. Call the GPT-4o API (using HttpClient, similar to Task 4). Return the generated response.", "testStrategy": "Write integration tests for the chat endpoint. Test with queries related to uploaded content and queries outside the content. Verify relevant context is retrieved from Qdrant and GPT-4o generates coherent responses based on the context. Test edge cases like no relevant context found.", "priority": "high", "dependencies": [1, 2, 4, 5, 9], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Create User Database Schema", "description": "Define and create the necessary database tables to store user information, including credentials and profile data.", "dependencies": [], "details": "Create a 'users' table with columns for id (primary key), email (unique), and hashed_password. Use a database migration tool (e.g., Alembic, Flyway) to manage the schema changes. Ensure an index is created on the email column for fast lookups.", "status": "pending", "testStrategy": "Run the migration and verify the table and columns are created correctly in a test database. Write a unit test for the User data model to ensure it can be instantiated and saved."}, {"id": 2, "title": "Implement Core Authentication Logic (Password & JWT)", "description": "Create reusable services for securely hashing/verifying passwords and for generating/validating JSON Web Tokens (JWTs).", "dependencies": [], "details": "Create a `PasswordService` using a strong hashing algorithm like bcrypt. It should have `hash(password)` and `verify(password, hash)` methods. Create a `TokenService` using a standard JWT library to generate tokens with a user ID payload and an expiration, and to validate incoming tokens using a secret key stored in environment variables.", "status": "pending", "testStrategy": "Write comprehensive unit tests for both services. For `PasswordService`, test hashing and successful/failed verification. For `TokenService`, test token generation, successful validation, and failed validation (e.g., bad signature, expired token)."}, {"id": 3, "title": "Develop User Registration and Login API Endpoints", "description": "Create the public-facing API endpoints for new user registration and existing user login.", "dependencies": [1, 2], "details": "Create a POST `/api/auth/register` endpoint that uses the `PasswordService` to hash the password and saves the new user via the model from Subtask 1. Create a POST `/api/auth/login` endpoint that finds the user, uses `PasswordService` to verify the password, and if successful, uses `TokenService` to generate and return a JWT.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. Test the full flow: successful registration, attempting to register a duplicate email, successful login, and failed login (wrong email/password). Assert that a valid JWT is returned on successful login."}]}, {"id": 11, "title": "Setup Frontend (React) Project", "description": "Set up the core React frontend project structure, including routing, state management, and necessary dependencies for UI components and API communication.", "details": "Create a new React project using Create React App or Vite. Set up basic routing (e.g., using React Router). Choose a state management library (e.g., Zustand, Redux Toolkit). Add libraries for UI components (e.g., Material UI, Ant Design) and API calls (e.g., Axios). Prepare for mobile responsiveness and theme switching.", "testStrategy": "Verify project builds and runs locally. Test basic routing setup. Ensure necessary libraries are installed and configured correctly.", "priority": "high", "dependencies": [], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Authentication Database Schema", "description": "Create the necessary database tables (e.g., 'users', 'roles') to store user credentials, profile information, and access control data.", "dependencies": [], "details": "The 'users' table should include columns for id, email, hashed_password, created_at, and updated_at. Use a database migration tool to script the schema changes. Ensure email column has a unique constraint.", "status": "pending", "testStrategy": "Run the migration script and verify the table structure and constraints directly in the database. Write a unit test for the migration to ensure it can be applied and reverted successfully."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/register) that allows new users to create an account.", "dependencies": [1], "details": "The endpoint must validate incoming data (e.g., valid email format, password strength). It should check for existing users with the same email. Use a strong hashing algorithm like bcrypt to salt and hash the password before storing it in the 'users' table.", "status": "pending", "testStrategy": "Write integration tests to cover successful registration, registration with a duplicate email, and registration with invalid data (e.g., weak password, invalid email format). Verify the password in the database is properly hashed."}, {"id": 3, "title": "Develop User Login API Endpoint and JWT Generation", "description": "Create an API endpoint (e.g., POST /api/auth/login) for users to authenticate and receive an access token.", "dependencies": [1], "details": "The endpoint should accept an email and password. It will find the user by email, compare the provided password with the stored hash, and if they match, generate a signed JSON Web Token (JWT). The JWT payload should include user ID and role. The token should have a reasonable expiration time.", "status": "pending", "testStrategy": "Write integration tests for successful login with correct credentials, failed login with an incorrect password, and failed login for a non-existent user. Unit test the JWT generation and verify its structure and signature."}, {"id": 4, "title": "Implement Authentication Middleware for Protected Routes", "description": "Create a middleware function that verifies the JWT on incoming requests to secure API endpoints, protecting them from unauthorized access.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization: Bearer <token>' header. It must validate the token's signature and check for expiration. If valid, decode the payload and attach the user's information to the request object for use in subsequent handlers. If invalid, it must return a 401 Unauthorized error.", "status": "pending", "testStrategy": "Create a sample protected endpoint. Write integration tests that attempt to access it with a valid token, an expired token, an invalid token, and no token, asserting the correct HTTP status code for each case."}, {"id": 5, "title": "Integrate Authentication Flow into the Front-End UI", "description": "Create and connect the front-end login and registration forms to the new API endpoints and manage the user's session state.", "dependencies": [2, 3, 4], "details": "Build UI components for registration and login forms. On form submission, make API calls to the backend. On successful login, store the received JWT securely (e.g., in an HttpOnly cookie or local storage) and update the UI state to reflect that the user is authenticated. Implement logic to attach the JWT to requests for protected resources.", "status": "pending", "testStrategy": "Use an end-to-end testing framework like Cypress or Playwright. Test the full user flow: registering a new user, logging in, being redirected, accessing a protected page, and logging out. Verify that UI elements for authenticated users appear and disappear correctly."}]}, {"id": 12, "title": "Implement Basic Chatbot UI Components", "description": "Develop the basic user interface for the chatbot, including input area, message display, and initial styling.", "details": "Create React components for the chat window, message bubbles (distinguishing user/AI messages), and a text input field with a send button. Implement basic styling, including support for dark/light themes as per UI/UX requirements. Ensure mobile responsiveness.", "testStrategy": "Manually test the UI layout on different screen sizes and devices. Verify input field and send button are functional (though not yet connected to backend). Test theme switching visually.", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Create User Database Schema", "description": "Define and create the necessary database table(s) to store user information, including credentials and profile data.", "dependencies": [], "details": "Create a 'users' table with columns: id (PK, auto-increment), email (unique, indexed), hashed_password (string), created_at (timestamp), and updated_at (timestamp). Use a database migration tool like Alembic or Flyway to manage schema changes.", "status": "pending", "testStrategy": "Run the migration against a test database and verify the 'users' table is created with the correct columns, types, and constraints. Write a unit test to confirm the table's existence programmatically."}, {"id": 2, "title": "Implement User Registration API Endpoint", "description": "Develop a public API endpoint (e.g., POST /api/register) to allow new users to create an account.", "dependencies": [1], "details": "The endpoint should accept an email and password. It must validate the input (e.g., valid email format, password complexity). Hash the password using bcrypt before storing the new user record in the database. Return a 201 Created status on success and appropriate error codes (e.g., 400 for bad request, 409 for duplicate email) on failure.", "status": "pending", "testStrategy": "Write integration tests for the happy path (successful registration) and failure cases (duplicate email, invalid input, weak password). Verify that the user is correctly inserted into the test database and that passwords are never stored in plain text."}, {"id": 3, "title": "Implement User Login and JWT Generation", "description": "Create an API endpoint (e.g., POST /api/login) for users to authenticate and receive a JSON Web Token (JWT).", "dependencies": [1, 2], "details": "The endpoint should accept an email and password. It will retrieve the user by email, verify the provided password against the stored hash using bcrypt.compare(). If valid, generate a signed JWT containing the user ID and an expiration claim. Return the JWT in the response body.", "status": "pending", "testStrategy": "Write integration tests using a pre-registered test user. Test successful login with correct credentials, verifying a valid JWT is returned. Test failed login attempts with an incorrect password and a non-existent user. Add a test to decode the returned JWT and validate its signature and payload."}]}, {"id": 13, "title": "Integrate Frontend Chat UI with Backend API", "description": "Connect the frontend chatbot UI to the backend Chat API to send user messages and display AI responses.", "details": "Implement logic in the frontend to capture user input from the text field and send it to the backend Chat API endpoint (Task 10) using Axios or fetch. Handle the API response and update the chat message display with the AI's reply. Implement basic loading states.", "testStrategy": "Perform end-to-end tests by sending messages through the UI. Verify requests reach the backend, responses are received, and messages appear correctly in the chat window. Test error handling for API failures.", "priority": "high", "dependencies": [12, 10], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the database table and corresponding application model for storing user data, including credentials and profile information.", "dependencies": [], "details": "Define a 'users' table with columns: id (PK), email (UNIQUE), username (UNIQUE), password_hash (string), created_at, and updated_at. Use a database migration tool to apply the schema. Create a User model in the ORM that maps to this table.", "status": "pending", "testStrategy": "Run database migrations and verify the table structure manually. Write unit tests for the User model to ensure constraints and properties are correctly defined."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Build a public API endpoint (e.g., POST /api/register) to allow new users to create an account.", "dependencies": [1], "details": "The endpoint should accept email, username, and password. Implement input validation for email format and password strength. Hash the password using bcrypt before storing it in the database. Return a 201 status on success and handle duplicate email/username errors with a 409 status.", "status": "pending", "testStrategy": "Write integration tests for successful registration, invalid input (e.g., bad email format), and attempts to register with an existing email or username."}, {"id": 3, "title": "Implement User Login and JWT Generation", "description": "Create an API endpoint (e.g., POST /api/login) for users to authenticate and receive a JSON Web Token (JWT).", "dependencies": [1], "details": "The endpoint will validate user credentials (email/password) against the database. On successful authentication, generate a signed JWT containing the user ID and an expiration claim. Return the JWT in the response body. Return a 401 status for failed login attempts.", "status": "pending", "testStrategy": "Write integration tests for successful login (verifying a valid JWT is returned) and failed login (incorrect password, non-existent user). Unit test the JWT generation and validation logic in isolation."}, {"id": 4, "title": "Create Authentication Middleware for Protected Routes", "description": "Develop middleware to verify the JWT on incoming requests to secure specific API endpoints.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization' header. It must validate the token's signature and check for expiration. If valid, attach the user's identity to the request object for downstream use. If invalid, it should respond with a 401 Unauthorized error.", "status": "pending", "testStrategy": "Create a sample protected endpoint. Write integration tests to confirm that requests with a valid JWT are allowed, while requests with no token, an invalid token, or an expired token are rejected with the correct HTTP status code."}]}, {"id": 14, "title": "Implement Admin Panel: Magazine Upload UI", "description": "Develop the frontend user interface for the Admin Panel specifically for uploading new magazine files.", "details": "Create React components for the Admin Panel section. Implement a file upload form with input fields for magazine metadata (if applicable) and a file input element. Add validation for file types (PDF/HTML).", "testStrategy": "Manually test the Admin Upload UI layout. Verify file input accepts correct types and rejects incorrect ones. Ensure form fields are present and functional.", "priority": "medium", "dependencies": [11], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Implement User Model and Registration Endpoint", "description": "Create the database schema for the user model and build the API endpoint for new user registration. This includes handling user input, validation, and password hashing.", "dependencies": [], "details": "Create a 'users' table with columns for id, email, username, and password_hash. Implement a POST /api/register endpoint that validates input (unique email, strong password) and uses a library like bcrypt to hash the password before storing the new user record.", "status": "pending", "testStrategy": "Unit test the user model's validation logic. Write integration tests for the /api/register endpoint to cover successful registration, duplicate email/username errors, and invalid input scenarios. Verify that stored passwords are properly hashed."}, {"id": 2, "title": "Develop User Login Endpoint and JWT Issuance", "description": "Create an API endpoint for users to log in. Upon successful authentication, generate and return a JSON Web Token (JWT) for session management.", "dependencies": [1], "details": "Implement a POST /api/login endpoint that accepts an email and password. The endpoint should find the user by email, compare the provided password with the stored hash using bcrypt. If credentials are valid, generate a signed JWT containing the user ID and an expiration date. Return the JWT in the response body.", "status": "pending", "testStrategy": "Write integration tests for the /api/login endpoint. Test successful login with correct credentials, ensuring a valid JWT is returned. Test login failures with incorrect passwords and non-existent emails, expecting appropriate error codes."}, {"id": 3, "title": "Implement Authentication Middleware for Protected Routes", "description": "Develop middleware that can be applied to API routes to ensure that only authenticated users can access them. The middleware will validate the JWT from the request header.", "dependencies": [2], "details": "Create a middleware function that extracts the JWT from the 'Authorization: Bearer <token>' header. It must verify the token's signature and check for expiration. If valid, decode the payload and attach the user's information to the request object. If invalid, return a 401 Unauthorized status. Apply this middleware to a sample protected route like GET /api/profile.", "status": "pending", "testStrategy": "Test a protected endpoint with the middleware applied. Verify that a request with a valid JWT receives a 200 OK response. Test requests with no token, an invalid token, or an expired token, and assert that they all receive a 401 Unauthorized response."}]}, {"id": 15, "title": "Integrate Admin Upload UI with Backend API", "description": "Connect the Admin Panel Magazine Upload UI to the backend Magazine Upload API endpoint.", "details": "Implement logic in the frontend Admin Panel to capture the selected file and metadata from the form (Task 14) and send it as a multipart form request to the backend Magazine Upload API endpoint (Task 6). Display upload progress or success/failure messages to the user.", "testStrategy": "Perform end-to-end tests by uploading files through the Admin UI. Verify requests reach the backend, files are processed, and success/failure feedback is displayed correctly in the UI. Check backend logs and database for successful processing.", "priority": "medium", "dependencies": [14, 6], "status": "pending", "subtasks": [{"id": 1, "title": "Design and Create User Database Schema", "description": "Define and implement the database table structure required for storing user information, including credentials and profile data.", "dependencies": [], "details": "Create a 'users' table with columns: 'id' (primary key), 'username' (unique), 'email' (unique), 'hashed_password', 'salt', 'created_at', and 'updated_at'. Ensure appropriate data types and constraints are used. The password must be hashed using a strong algorithm like bcrypt.", "status": "pending", "testStrategy": "Verify table creation and schema correctness using a database client. Write a unit test to insert a sample user record and confirm that all constraints (e.g., uniqueness) are enforced correctly."}, {"id": 2, "title": "Develop Backend Authentication API Endpoints", "description": "Build the server-side API endpoints for user registration, login, and logout functionalities.", "dependencies": [1], "details": "Implement three endpoints: POST /api/register, POST /api/login, and POST /api/logout. The register endpoint should hash the password before saving the user. The login endpoint should validate credentials and return a session token (e.g., JWT). The logout endpoint should invalidate the token.", "status": "pending", "testStrategy": "Use an API testing tool like Postman or Insomnia. Write integration tests for each endpoint. Test success cases (valid registration/login), failure cases (duplicate username, incorrect password), and edge cases (malformed requests)."}, {"id": 3, "title": "Build Frontend Login and Registration Forms", "description": "Create the user interface components for the login and registration pages and integrate them with the backend API.", "dependencies": [2], "details": "Using a frontend framework (e.g., React, Vue), create two forms: one for registration and one for login. Implement form validation for fields like email format and password strength. On submit, make asynchronous calls to the corresponding API endpoints. Handle API responses to show success/error messages and manage user session state (e.g., storing the JWT in localStorage).", "status": "pending", "testStrategy": "Perform end-to-end testing in a browser. Test the complete user flow: registration, receiving an error for a duplicate email, logging in with the new credentials, and logging out. Verify that UI elements update correctly based on API responses and that protected routes are inaccessible when logged out."}]}, {"id": 16, "title": "Implement secure GPT-4o API key management", "description": "Securely manage API keys for GPT-4o integration using environment variables and secrets management.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Develop Backend API for User Authentication", "description": "Create and expose secure API endpoints for user registration, login, and session validation. This forms the core of the authentication system.", "dependencies": [], "details": "Implement three main endpoints: POST /api/register, POST /api/login, and GET /api/user/me. Use bcrypt for password hashing. Upon successful login, generate a JWT (JSON Web Token) containing the user ID and role. The /api/user/me endpoint should be protected and return user data based on a valid JWT.", "status": "pending", "testStrategy": "Unit test the password hashing and JWT generation logic. Use an API client like Postman to perform integration tests on the registration and login flows, ensuring correct status codes and payload responses."}, {"id": 2, "title": "Build Frontend Authentication UI Components", "description": "Create the user interface for the Login and Registration pages, including forms, input validation, and state management.", "dependencies": [1], "details": "Using a frontend framework (e.g., React, Vue), build two separate pages/components: one for login and one for registration. Implement client-side validation for email format, password complexity, and required fields. On form submission, call the backend API endpoints created in subtask 1. Handle API responses to display success or error messages to the user.", "status": "pending", "testStrategy": "Use a component testing library like Jest with React Testing Library to test form rendering and validation logic. Manually test the complete user flow in a browser, checking for correct error handling and successful navigation."}, {"id": 3, "title": "Implement Protected Routes and Session Management", "description": "Secure application routes to ensure they are only accessible by authenticated users. Manage the user's session state throughout the application.", "dependencies": [1, 2], "details": "On the frontend, create a route guard or higher-order component that checks for a valid JWT before rendering a protected component. If no valid token exists, redirect the user to the login page. Store the JWT securely (e.g., in an HttpOnly cookie or local storage) after login and remove it on logout. Implement a global state (e.g., using Context API or Redux) to hold the current user's authentication status.", "status": "pending", "testStrategy": "Write end-to-end tests using a tool like Cypress or Playwright. Test scenarios include: attempting to access a protected route while logged out (should redirect to login), logging in and accessing the protected route successfully, and logging out and being denied access to the protected route again."}]}, {"id": 17, "title": "Add reporting features to admin panel", "description": "Implement usage statistics and reporting in the admin dashboard.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables and columns to store user information, including credentials and profile data.", "dependencies": [], "details": "Create a 'users' table with columns: 'id' (UUI<PERSON>, primary key), 'email' (VARCHAR, unique, not null), 'password_hash' (VARCHAR, not null), 'created_at' (TIMESTAMP), and 'updated_at' (TIMESTAMP). Use a database migration tool like Alembic or Flyway to manage schema changes.", "status": "pending", "testStrategy": "Run the migration against a test database. Verify the table and columns are created correctly with the specified constraints. Write a unit test to confirm that a model object can be created, saved, and retrieved from the table."}, {"id": 2, "title": "Develop API Endpoints for Registration and Login", "description": "Build the backend API endpoints for user registration and authentication, including token generation.", "dependencies": [1], "details": "Create a POST '/api/auth/register' endpoint to handle new user creation (hashing passwords with bcrypt). Create a POST '/api/auth/login' endpoint to authenticate users and issue a JWT (JSON Web Token) upon success. Implement input validation for both endpoints.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. Test success cases (201 Created, 200 OK) and failure cases (400 Bad Request for invalid data, 409 Conflict for duplicate email, 401 Unauthorized for bad credentials). Validate the structure and signature of the generated JWT."}, {"id": 3, "title": "Create Frontend UI for Login and Registration Forms", "description": "Build the user interface components for the login and registration pages that interact with the backend API.", "dependencies": [2], "details": "Using a frontend framework like React or Vue, create two routes: '/login' and '/register'. Each route should display a form with the necessary input fields. Implement client-side validation. On form submission, call the respective API endpoints. Handle API responses to display success/error messages and redirect the user upon successful login, storing the JWT in a secure manner (e.g., HttpOnly cookie).", "status": "pending", "testStrategy": "Perform end-to-end testing by navigating to the registration page, creating a user, and then using those credentials to log in. Write component tests for the forms to verify rendering and validation logic. Use a mock API to test the data submission and response handling logic in isolation."}]}, {"id": 18, "title": "Implement user management in admin panel", "description": "Add user roles, permissions, and management features.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables to store user information, including credentials, roles, and profile data.", "dependencies": [], "details": "Define a 'users' table with columns for id, username, email, and hashed_password. Use a migration tool like Alembic or Flyway to create and manage the schema. Ensure email and username fields have unique constraints.", "status": "pending", "testStrategy": "Run the database migration and verify the table structure and constraints are created correctly. Write a test to ensure the migration can be applied and reverted successfully."}, {"id": 2, "title": "Implement JWT Generation and Validation Service", "description": "Create a reusable service for generating, signing, and validating JSON Web Tokens (JWTs) for authenticated users.", "dependencies": [], "details": "Use a standard JWT library. The service should have a 'generateToken' function that takes a user ID and an 'validateToken' function that verifies the signature and expiration. Store the JWT secret key securely in environment variables, not in code.", "status": "pending", "testStrategy": "Unit test the service. Test token generation with a known payload. Test validation with a valid token, an invalid token (bad signature), an expired token, and a malformed token."}, {"id": 3, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/register) that allows new users to create an account.", "dependencies": [1], "details": "The endpoint should accept username, email, and password. Validate input for correctness and password strength. Hash the password using a strong algorithm like bcrypt before storing it in the database. Return a success message or user object, but not the password.", "status": "pending", "testStrategy": "Integration test the endpoint. Test successful registration. Test validation errors for duplicate email/username, weak password, and missing fields. Verify that the password stored in the database is correctly hashed."}, {"id": 4, "title": "Develop User Login API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/login) for users to authenticate and receive an access token.", "dependencies": [1, 2], "details": "The endpoint should accept user credentials (email/username and password). Find the user in the database and compare the provided password with the stored hash. If valid, generate a JWT using the service from subtask 2 and return it to the client.", "status": "pending", "testStrategy": "Integration test the endpoint. Test successful login with correct credentials, verifying a valid JWT is returned. Test failed login attempts with an incorrect password or a non-existent user."}, {"id": 5, "title": "Create Authentication Middleware for Protected Routes", "description": "Implement middleware to intercept requests, validate the JWT from the Authorization header, and attach user information to the request object.", "dependencies": [2], "details": "The middleware should extract the token from the 'Authorization: Bearer <token>' header. Use the JWT service to validate it. If valid, decode the payload and attach the user ID to the request context. If invalid or missing, return a 401 Unauthorized error.", "status": "pending", "testStrategy": "Apply the middleware to a test route. Send requests with a valid token, an invalid token, an expired token, and no token, and assert that the correct HTTP status code and response are returned in each case."}, {"id": 6, "title": "Implement Password Reset Functionality", "description": "Create the API endpoints and logic for a secure password reset flow, typically involving email verification.", "dependencies": [1], "details": "Create two endpoints: 1) POST /api/auth/forgot-password: generates a single-use reset token and sends a reset link via email. 2) POST /api/auth/reset-password: takes the token and a new password, validates the token, and updates the user's password hash.", "status": "pending", "testStrategy": "Integration test the entire flow, mocking the email service. Test requesting a reset for a valid and invalid email. Test resetting the password with a valid token and an expired/invalid token. Verify the password is changed in the database."}]}, {"id": 19, "title": "Implement reporting and analytics", "description": "Track and report chatbot usage, queries, and performance metrics.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table(s) to store user information, including credentials and profile data, using a migration tool.", "dependencies": [], "details": "Define a 'users' table with columns: id (primary key), username (unique, indexed), email (unique, indexed), password_hash (string), created_at (timestamp), updated_at (timestamp). Use a database migration tool like Flyway or Alembic to script and apply the schema changes.", "status": "pending", "testStrategy": "Verify the migration runs successfully against a test database. Manually inspect the schema to confirm all columns, types, and constraints (unique, not null) are correct. Write a unit test to ensure the User model object can be created and saved."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint that allows new users to create an account.", "dependencies": [1], "details": "Implement a POST endpoint at `/api/auth/register`. It must accept `username`, `email`, and `password`. Perform input validation: check for required fields, valid email format, and enforce a password strength policy. Use a strong hashing algorithm like bcrypt to hash the password before storing it in the database. Return a 201 Created status with the new user's ID and username upon success.", "status": "pending", "testStrategy": "Write integration tests to cover: successful registration, attempted registration with a duplicate username or email, registration with missing or invalid input (e.g., weak password, bad email format), and verify that the password stored in the database is correctly hashed and not plaintext."}, {"id": 3, "title": "Implement JWT Generation and Validation Service", "description": "Create a reusable service or utility for creating and verifying JSON Web Tokens (JWTs) for stateless authentication.", "dependencies": [], "details": "Create a dedicated 'AuthService' or 'JwtUtil'. It should have a `generateToken` method that takes a user ID and role as a payload, signs it with a secret key, and sets an expiration time (e.g., 1 hour). It should also have a `verifyToken` method that decodes a token and validates its signature and expiration. The JWT secret key must be stored securely in an environment variable, not in code.", "status": "pending", "testStrategy": "Write unit tests for the service. Test token generation to ensure it produces a valid JWT string. Test token verification with a valid token, an expired token, a token signed with the wrong key, and a malformed token. Ensure payload data is correctly encoded and decoded."}, {"id": 4, "title": "Develop User Login API Endpoint", "description": "Create a public API endpoint for existing users to authenticate and receive a JWT.", "dependencies": [1, 3], "details": "Implement a POST endpoint at `/api/auth/login`. It should accept `email` and `password`. Retrieve the user from the database by email. Use bcrypt's compare function to verify the provided password against the stored hash. If credentials are valid, use the JWT service (from subtask 3) to generate a token and return it in the response body.", "status": "pending", "testStrategy": "Write integration tests for: successful login with correct credentials, failed login with an incorrect password, and failed login for a non-existent user. On success, verify that the response contains a structurally valid JWT."}, {"id": 5, "title": "Create Authentication Middleware for Protected Routes", "description": "Implement middleware that intercepts requests to secure endpoints, checking for a valid JWT to ensure the user is authenticated.", "dependencies": [3], "details": "Create a middleware function (e.g., `requireAuth`). It should extract the JWT from the `Authorization` header (e.g., 'Bearer <token>'). Use the JWT service (from subtask 3) to verify the token. If the token is valid, attach the decoded user payload (e.g., user ID) to the request object (e.g., `req.user`) and pass control to the next handler. If the token is missing, invalid, or expired, the middleware must immediately respond with a 401 Unauthorized error.", "status": "pending", "testStrategy": "Create a test-only protected endpoint. Write integration tests to verify that a request with a valid token is allowed to pass, while requests with no token, an invalid token, or an expired token are rejected with a 401 status code. Confirm that the user payload is available in the request context for downstream handlers."}, {"id": 6, "title": "Implement 'Get User Profile' Protected Endpoint", "description": "Create a protected API endpoint that returns the profile information for the currently authenticated user.", "dependencies": [1, 5], "details": "Implement a GET endpoint at `/api/users/me`. Apply the authentication middleware (from subtask 5) to this route. Inside the route handler, use the user ID from the request context (e.g., `req.user.id`) to fetch the corresponding user's data from the database. Return the user's profile information (e.g., id, username, email, created_at), making sure to exclude sensitive data like the password hash.", "status": "pending", "testStrategy": "Write integration tests. First, obtain a valid JWT by calling the login endpoint. Then, use that token to make a request to `/api/users/me` and assert that the correct user data is returned with a 200 OK status. Test that making a request without a token results in a 401 Unauthorized error."}]}, {"id": 20, "title": "Setup DevOps pipeline with Azure DevOps", "description": "Configure CI/CD pipelines, Docker containers, and deployment to Azure.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table(s) to store user information, including credentials and profile data.", "dependencies": [], "details": "Define a 'users' table with columns for id (primary key), email (unique), username, password_hash (string), created_at, and updated_at. Use a migration script to create and apply the schema. Ensure the password_hash column is long enough for bcrypt hashes.", "status": "pending", "testStrategy": "Verify the migration runs successfully and the table is created with the correct columns and constraints in a test database. No direct application testing needed for this schema-only task."}, {"id": 2, "title": "Develop User Registration and Login API Endpoints", "description": "Build the API endpoints for user registration and login, including password hashing and authentication logic.", "dependencies": [1], "details": "Create a POST /api/auth/register endpoint to accept email and password, validate input, hash the password using bcrypt, and save the new user. Create a POST /api/auth/login endpoint to verify credentials and, upon success, issue a JSON Web Token (JWT) containing the user ID and an expiration claim.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. For registration, test successful creation, duplicate email errors, and invalid input. For login, test with valid credentials (expect JWT), invalid password (expect 401), and non-existent user (expect 404 or 401)."}, {"id": 3, "title": "Implement Authentication Middleware for Protected Routes", "description": "Create a middleware function to verify the JWT on incoming requests and protect specific API routes from unauthorized access.", "dependencies": [2], "details": "The middleware should extract the JWT from the 'Authorization: Bearer <token>' header. It must validate the token's signature and check for expiration. If valid, decode the payload and attach the user's identity to the request object. If invalid, it must respond with a 401 Unauthorized status.", "status": "pending", "testStrategy": "Apply the middleware to a test route. Write integration tests to access the route with a valid token (should succeed), an invalid/expired token (should fail with 401), and no token (should fail with 401)."}]}, {"id": 21, "title": "Implement security measures and error handling", "description": "Add authentication checks, input validation, error logging, and security best practices.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database table(s) to store user information, including credentials and profile data. This forms the foundation for all user-related features.", "dependencies": [], "details": "Define a 'users' table with columns: 'id' (UUID, primary key), 'email' (varchar, unique), 'password_hash' (varchar), 'created_at' (timestamp), and 'updated_at' (timestamp). Use a database migration tool like Alembic or Flyway to script and apply the schema changes.", "status": "pending", "testStrategy": "Run the migration against a test database and verify that the 'users' table is created with the correct columns, types, and constraints. Write a unit test to ensure the User model can be instantiated and saved."}, {"id": 2, "title": "Develop API Endpoints for User Registration and Login", "description": "Create the backend API endpoints for new user registration and existing user login. These endpoints will handle user creation and session authentication.", "dependencies": [1], "details": "Implement a POST /api/v1/register endpoint that validates input, hashes the password using bcrypt, and saves the new user to the database. Implement a POST /api/v1/login endpoint that validates credentials and returns a JWT upon success. Ensure proper error handling for cases like duplicate emails or incorrect passwords.", "status": "pending", "testStrategy": "Write integration tests for both endpoints. For registration, test success cases, duplicate email errors, and invalid input. For login, test successful authentication (verifying JWT payload), incorrect password, and non-existent user scenarios."}, {"id": 3, "title": "Create Frontend UI for Registration and Login Forms", "description": "Build the user interface components for the registration and login pages using a frontend framework like React or Vue. This will be the user-facing part of the authentication system.", "dependencies": [2], "details": "Create two routes: /login and /register. Each route should display a form with input fields for email and password. Implement client-side validation for immediate feedback. On submit, call the respective API endpoints. On successful login, store the JWT in local storage and redirect the user to the dashboard.", "status": "pending", "testStrategy": "Use a component testing library like Jest/RTL to test form rendering and validation logic. Perform end-to-end testing using Cypress or Playwright to simulate the full user flow: navigating to the page, filling the form, submitting, and verifying the subsequent redirect and session state."}]}, {"id": 22, "title": "Enhance frontend with themes and mobile responsiveness", "description": "Implement responsive design, themes, and UI improvements for better user experience.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables and columns to store user information, credentials, and roles.", "dependencies": [], "details": "Define a 'users' table with columns for id, email, hashed_password, created_at, and updated_at. Use a database migration tool to create and manage the schema. Ensure email is unique.", "status": "pending", "testStrategy": "Run the migration and verify table creation in the database. Write a test to ensure the migration can be successfully applied and rolled back."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/register) to allow new users to sign up.", "dependencies": [1], "details": "The endpoint must validate input data (email format, password strength). It should check for existing users to prevent duplicates. Hash the user's password using bcrypt before storing it. Return a success response upon creation.", "status": "pending", "testStrategy": "Write integration tests to simulate registration with valid data, invalid data (e.g., bad email), and duplicate data. Verify correct HTTP status codes (201, 400, 409) and database state after each test."}, {"id": 3, "title": "Develop User Login API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/login) to authenticate users and issue access tokens.", "dependencies": [1], "details": "The endpoint will accept an email and password. It will find the user by email, compare the provided password with the stored hash, and if they match, generate a signed JSON Web Token (JWT) with a reasonable expiration time.", "status": "pending", "testStrategy": "Write integration tests for successful login (verifying a valid JWT is returned) and failed login attempts (wrong password, non-existent user), ensuring a 401 Unauthorized status is returned for failures."}, {"id": 4, "title": "Implement Authentication Middleware for Protected Routes", "description": "Create a middleware to verify the JWT on incoming requests to secure specific endpoints.", "dependencies": [3], "details": "The middleware should extract the JWT from the 'Authorization' header. It must verify the token's signature and check for expiration. If valid, attach the user's payload (e.g., user ID) to the request object. If invalid, return a 401 or 403 error.", "status": "pending", "testStrategy": "Unit test the token validation logic. Write integration tests for a mock protected route, testing requests with a valid token, an invalid token, an expired token, and no token."}, {"id": 5, "title": "Create Protected 'Get User Profile' Endpoint", "description": "Develop a protected endpoint (e.g., GET /api/users/me) that returns the currently authenticated user's profile data.", "dependencies": [4], "details": "Apply the authentication middleware to this route. The route handler will use the user ID from the request payload (added by the middleware) to fetch the user's data from the database. Ensure sensitive information like the password hash is not returned.", "status": "pending", "testStrategy": "Write an end-to-end test that first logs in a user to obtain a JWT, then uses that token to access this endpoint. Verify that the correct user profile is returned and that unauthenticated requests are blocked."}]}, {"id": 23, "title": "Implement notification system", "description": "Add real-time notifications for admin and users.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Database Schema", "description": "Create the necessary database tables and columns to store user information, including credentials and profile data, using a migration script.", "dependencies": [], "details": "Define a 'users' table with columns: id (PK), email (UNIQUE), username (UNIQUE), password_hash (string), created_at (timestamp), and updated_at (timestamp). Use a database migration tool (e.g., Flyway, Alembic) to create and version the schema.", "status": "pending", "testStrategy": "Run the migration script and verify the table structure and constraints in a test database. Write a unit test for the User model to ensure it can be instantiated and saved correctly."}, {"id": 2, "title": "Develop User Registration API Endpoint", "description": "Create a public API endpoint (e.g., POST /api/auth/register) to handle new user sign-ups.", "dependencies": [1], "details": "The endpoint must validate incoming data (email format, password strength), hash the password using bcrypt, check for duplicate email/username, and insert the new user record into the database. Return a 201 status on success or appropriate error codes (e.g., 400, 409).", "status": "pending", "testStrategy": "Write integration tests to cover successful registration, registration with duplicate credentials, and registration with invalid data. Assert that the password stored in the database is a valid hash and not plaintext."}, {"id": 3, "title": "Develop User Login API Endpoint and JWT Generation", "description": "Create a public API endpoint (e.g., POST /api/auth/login) to authenticate users and issue a JSON Web Token (JWT).", "dependencies": [1], "details": "The endpoint should accept user credentials (email/username and password), verify them against the stored password hash, and upon successful authentication, generate a signed JWT containing user identifiers (e.g., user ID, role) and an expiration time.", "status": "pending", "testStrategy": "Integration tests for successful login (verifying a valid JWT is returned), login with incorrect credentials (verifying a 401 error), and login for a non-existent user. Unit test the JWT generation logic separately."}, {"id": 4, "title": "Implement Authentication Middleware for Protected Routes", "description": "Create a middleware to verify the JWT on incoming requests to secure specific API endpoints.", "dependencies": [3], "details": "The middleware will extract the JWT from the 'Authorization' header, validate its signature and expiration, and decode it to identify the user. If valid, attach user information to the request object and pass it to the next handler. If invalid, return a 401 Unauthorized response. Create a sample protected route (e.g., GET /api/me) to test.", "status": "pending", "testStrategy": "Write integration tests for the protected route: test access with a valid token, an expired token, a malformed token, and no token. Ensure correct HTTP status codes are returned in each case."}, {"id": 5, "title": "Build Frontend Registration UI Component", "description": "Create the user interface for the registration form, including input fields, validation, and API integration.", "dependencies": [2], "details": "Develop a responsive form with fields for username, email, and password. Implement client-side validation to provide immediate feedback. On submit, send a request to the registration endpoint (Task 2). Handle success (e.g., redirect to login) and error (e.g., display error messages) responses.", "status": "pending", "testStrategy": "Use component tests to verify form rendering and validation logic. Use end-to-end (E2E) tests to simulate a full registration flow, mocking the API response to test both success and failure UI states."}, {"id": 6, "title": "Build Frontend Login UI and State Management", "description": "Create the user interface for the login form and manage the application's authentication state.", "dependencies": [3, 4], "details": "Develop a login form that calls the login endpoint (Task 3). On successful login, securely store the received JWT (e.g., in an HttpOnly cookie) and update a global state management store (e.g., Redux, Zustand) to reflect the user's authenticated status. Use this state to conditionally render UI elements and control access to protected frontend routes.", "status": "pending", "testStrategy": "Component tests for the login form. E2E tests to simulate a user logging in, verifying the token is stored, the global state is updated, and the user is redirected to a protected page. Test failed login attempts."}]}, {"id": 24, "title": "Create test plan and implement unit tests", "description": "Develop comprehensive testing strategy and write unit tests for all components.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Create User Authentication Database Schema", "description": "Define and create the necessary database tables to store user information, including credentials and profile data.", "dependencies": [], "details": "Create a `users` table with columns like `id` (PK), `email` (UNIQUE), `password_hash` (string), `created_at`, and `updated_at`. Use a database migration tool (e.g., Alembic, Flyway) to script the changes for version control and easy deployment.", "status": "pending", "testStrategy": "Run the database migration up and down to ensure it's reversible. Manually inspect the database schema to confirm all tables, columns, and constraints (e.g., uniqueness) are created correctly."}, {"id": 2, "title": "Develop API Endpoints for User Registration and Login", "description": "Create the backend API endpoints that allow new users to register and existing users to log in securely.", "dependencies": [1], "details": "Implement a POST `/api/auth/register` endpoint to create a new user, ensuring proper input validation and password hashing (e.g., with bcrypt). Implement a POST `/api/auth/login` endpoint to verify credentials and return a session token (e.g., JWT) upon success.", "status": "pending", "testStrategy": "Write unit tests for input validation and password hashing logic. Write integration tests to call the endpoints, verifying successful registration/login, and proper error handling for duplicate emails or incorrect credentials."}, {"id": 3, "title": "Implement Authentication Middleware for Protected Routes", "description": "Create middleware to verify a user's authentication token before allowing access to protected API routes.", "dependencies": [2], "details": "The middleware should extract the JWT from the `Authorization: Bearer <token>` header. It must validate the token's signature and expiration. If valid, attach the user's identity to the request context. If invalid, it must return a `401 Unauthorized` error response.", "status": "pending", "testStrategy": "Create a sample protected route. Write integration tests that attempt to access this route with a valid token, an invalid/malformed token, an expired token, and no token, asserting that the correct HTTP status code (200 or 401) is returned for each case."}]}, {"id": 25, "title": "Document deployment process", "description": "Create Deployment.md with instructions for production setup.", "details": "", "testStrategy": "", "status": "pending", "dependencies": [], "priority": "medium", "subtasks": [{"id": 1, "title": "Design and Implement User Model and Registration Endpoint", "description": "Create the database schema for the user model and build the API endpoint for new user registration, including input validation and password hashing.", "dependencies": [], "details": "Define a 'users' table with columns for id, username, email, and password_hash. The POST /api/auth/register endpoint should validate input (unique email, password strength), hash the password using bcrypt, and store the new user record.", "status": "pending", "testStrategy": "Unit test the user model validations. Integration test the registration endpoint for success (201 Created) and failure cases (400 Bad Request for duplicate email, 422 for invalid data)."}, {"id": 2, "title": "Develop Login Endpoint and JWT Issuance", "description": "Create an API endpoint for user login that authenticates credentials and issues a signed JSON Web Token (JWT) upon success.", "dependencies": [1], "details": "Implement a POST /api/auth/login endpoint. It should find the user by email, verify the provided password against the stored hash, and if valid, generate a signed JWT containing the user ID and an expiration claim. Return the JWT in the response.", "status": "pending", "testStrategy": "Integration test the login endpoint. Verify successful login returns a valid JWT. Test failed attempts (wrong password, user not found) return a 401 Unauthorized status and no token."}, {"id": 3, "title": "Implement Authentication Middleware for Protected Routes", "description": "Develop middleware to verify the JWT from the Authorization header and protect specific API routes from unauthenticated access.", "dependencies": [2], "details": "The middleware should parse the 'Bearer' token from the Authorization header. It must verify the token's signature and check for expiration. If valid, attach the user payload to the request object and proceed. If invalid, respond with a 401 Unauthorized error.", "status": "pending", "testStrategy": "Create a test protected endpoint. Write integration tests to confirm access is granted with a valid token and denied (401) with no token, an invalid token, or an expired token."}]}], "metadata": {"created": "2025-07-14T05:37:21.264Z", "updated": "2025-07-14T11:22:24.888Z", "description": "Tasks for master context"}}}