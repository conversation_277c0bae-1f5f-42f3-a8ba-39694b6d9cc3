version: '3.8'

services:
  # Backend API
  backend:
    build:
      context: ./Backend
      dockerfile: Dockerfile
    ports:
      - "5000:80"
      - "5001:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
    depends_on:
      - mongodb
      - redis
      - qdrant
    volumes:
      - ./Backend/appsettings.json:/app/appsettings.json
    networks:
      - niooai-network

  # MongoDB Database
  mongodb:
    image: mongo:7.0
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=niooai_database
    volumes:
      - mongodb_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - niooai-network

  # Redis Cache
  redis:
    image: redis:7.0-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - niooai-network

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:v1.9.0
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334
    networks:
      - niooai-network

  # MongoDB Express (Optional - for database management)
  mongo-express:
    image: mongo-express:1.0.0
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=password123
      - ME_CONFIG_MONGODB_URL=*****************************************/
      - ME_CONFIG_BASICAUTH=false
    depends_on:
      - mongodb
    networks:
      - niooai-network

volumes:
  mongodb_data:
  redis_data:
  qdrant_data:

networks:
  niooai-network:
    driver: bridge 