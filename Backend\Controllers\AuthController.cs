using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.IdentityModel.Tokens;
using MongoDB.Driver;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Backend.Interfaces;
using Backend.Models;
using System.Security.Cryptography;

namespace Backend.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase
    {
        private readonly IUserRepository _userRepository;
        private readonly IConfiguration _configuration;
        private readonly ILogger<AuthController> _logger;

        public AuthController(
            IUserRepository userRepository,
            IConfiguration configuration,
            ILogger<AuthController> logger)
        {
            _userRepository = userRepository;
            _configuration = configuration;
            _logger = logger;
        }

        [HttpPost("register")]
        public async Task<IActionResult> Register([FromBody] RegisterRequest request)
        {
            try
            {
                _logger.LogInformation("User registration attempt for email: {Email}", request.Email);
                
                // Validate input
                if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { message = "Email and password are required" });
                }

                if (request.Password.Length < 6)
                {
                    return BadRequest(new { message = "Password must be at least 6 characters long" });
                }

                // Check if user exists
                if (await _userRepository.EmailExistsAsync(request.Email))
                {
                    return Conflict(new { message = "User with this email already exists" });
                }

                // Hash password
                var hashedPassword = HashPassword(request.Password);

                // Create user
                var user = new User
                {
                    Email = request.Email.ToLowerInvariant(),
                    HashedPassword = hashedPassword,
                    FirstName = request.FirstName,
                    LastName = request.LastName,
                    Role = UserRole.User,
                    IsActive = true,
                    EmailVerified = false
                };

                var createdUser = await _userRepository.CreateUserAsync(user);

                // Generate JWT token
                var token = GenerateJwtToken(createdUser.Id!, createdUser.Email);

                _logger.LogInformation("User registered successfully: {UserId}", createdUser.Id);

                return Ok(new { 
                    message = "User registered successfully",
                    token = token,
                    user = new {
                        id = createdUser.Id,
                        email = createdUser.Email,
                        firstName = createdUser.FirstName,
                        lastName = createdUser.LastName,
                        role = createdUser.Role.ToString(),
                        emailVerified = createdUser.EmailVerified
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during user registration");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginRequest request)
        {
            try
            {
                _logger.LogInformation("Login attempt for email: {Email}", request.Email);

                // Validate input
                if (string.IsNullOrEmpty(request.Email) || string.IsNullOrEmpty(request.Password))
                {
                    return BadRequest(new { message = "Email and password are required" });
                }

                // Get user by email
                var user = await _userRepository.GetUserByEmailAsync(request.Email.ToLowerInvariant());
                if (user == null)
                {
                    return Unauthorized(new { message = "Invalid email or password" });
                }

                // Verify password
                if (!VerifyPassword(request.Password, user.HashedPassword))
                {
                    return Unauthorized(new { message = "Invalid email or password" });
                }

                // Check if email is verified
                if (!user.EmailVerified)
                {
                    return Unauthorized(new { message = "Please verify your email address before logging in" });
                }

                // Update last login
                await _userRepository.UpdateLastLoginAsync(user.Id!);

                // Generate JWT token
                var token = GenerateJwtToken(user.Id!, user.Email);

                _logger.LogInformation("User logged in successfully: {UserId}", user.Id);

                return Ok(new { 
                    message = "Login successful",
                    token = token,
                    user = new {
                        id = user.Id,
                        email = user.Email,
                        firstName = user.FirstName,
                        lastName = user.LastName,
                        role = user.Role.ToString(),
                        emailVerified = user.EmailVerified,
                        lastLoginAt = user.LastLoginAt
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("google")]
        public IActionResult GoogleLogin()
        {
            // TODO: Implement Google OAuth2 redirect
            return Challenge("Google");
        }

        [HttpGet("google/callback")]
        public async Task<IActionResult> GoogleCallback()
        {
            try
            {
                // TODO: Implement Google OAuth2 callback
                // - Handle Google authentication result
                // - Create or update user in MongoDB
                // - Generate JWT token
                // - Redirect to frontend with token

                _logger.LogInformation("Google OAuth callback received");

                return Ok(new { message = "Google OAuth callback - to be implemented" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Google OAuth callback");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        [HttpGet("profile")]
        [Authorize]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return Unauthorized(new { message = "Invalid token" });
                }

                _logger.LogInformation("Profile request for user: {UserId}", userId);

                var user = await _userRepository.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { message = "User not found" });
                }

                return Ok(new { 
                    message = "Profile retrieved successfully",
                    user = new {
                        id = user.Id,
                        email = user.Email,
                        firstName = user.FirstName,
                        lastName = user.LastName,
                        role = user.Role.ToString(),
                        emailVerified = user.EmailVerified,
                        isActive = user.IsActive,
                        createdAt = user.CreatedAt,
                        lastLoginAt = user.LastLoginAt,
                        profilePictureUrl = user.ProfilePictureUrl
                    }
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile");
                return StatusCode(500, new { message = "Internal server error" });
            }
        }

        private string GenerateJwtToken(string userId, string email)
        {
            var jwtSettings = _configuration.GetSection("Authentication:Jwt");
            var secretKey = Encoding.UTF8.GetBytes(jwtSettings["SecretKey"]);
            
            var claims = new[]
            {
                new Claim(ClaimTypes.NameIdentifier, userId),
                new Claim(ClaimTypes.Email, email),
                new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
                new Claim(JwtRegisteredClaimNames.Iat, 
                    new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString(), 
                    ClaimValueTypes.Integer64)
            };

            var key = new SymmetricSecurityKey(secretKey);
            var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);
            var expirationHours = int.Parse(jwtSettings["ExpirationHours"]);

            var token = new JwtSecurityToken(
                issuer: jwtSettings["Issuer"],
                audience: jwtSettings["Audience"],
                claims: claims,
                expires: DateTime.UtcNow.AddHours(expirationHours),
                signingCredentials: credentials
            );

            return new JwtSecurityTokenHandler().WriteToken(token);
        }

        private string HashPassword(string password)
        {
            using (var sha256 = SHA256.Create())
            {
                // Add salt to password for better security
                var saltedPassword = password + "NiooAI_Salt_2024";
                var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(saltedPassword));
                return Convert.ToBase64String(hashedBytes);
            }
        }

        private bool VerifyPassword(string password, string hashedPassword)
        {
            var hashOfInput = HashPassword(password);
            return hashOfInput == hashedPassword;
        }
    }

    public class RegisterRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
    }

    public class LoginRequest
    {
        public string Email { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
    }
} 