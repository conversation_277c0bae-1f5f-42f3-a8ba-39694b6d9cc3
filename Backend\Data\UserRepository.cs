using Backend.Interfaces;
using Backend.Models;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using MongoDB.Bson;

namespace Backend.Data
{
    public class UserRepository : IUserRepository
    {
        private readonly IMongoCollection<User> _users;

        public UserRepository(IOptions<DatabaseSettings> databaseSettings, IMongoClient mongoClient)
        {
            var database = mongoClient.GetDatabase(databaseSettings.Value.DatabaseName);
            _users = database.GetCollection<User>(databaseSettings.Value.UsersCollectionName);

            // Indexes are created by MongoDB init script (scripts/mongo-init.js)
            // No need to create them here to avoid conflicts
        }

        private void CreateIndexes()
        {
            try
            {
                // Fetch existing index names to avoid duplicate creation
                var existingIndexNames = _users.Indexes
                                                .List()
                                                .ToList()
                                                .Select(i => i["name"].AsString)
                                                .ToHashSet();

                var newIndexes = new List<CreateIndexModel<User>>();

                // Unique index on email
                if (!existingIndexNames.Contains("email_1"))
                {
                    var emailIndex = new CreateIndexModel<User>(
                        Builders<User>.IndexKeys.Ascending(u => u.Email),
                        new CreateIndexOptions { Unique = true, Name = "email_1" });
                    newIndexes.Add(emailIndex);
                }

                // Sparse index on GoogleId
                if (!existingIndexNames.Contains("googleId_1"))
                {
                    var googleIdIndex = new CreateIndexModel<User>(
                        Builders<User>.IndexKeys.Ascending(u => u.GoogleId),
                        new CreateIndexOptions { Sparse = true, Name = "googleId_1" });
                    newIndexes.Add(googleIdIndex);
                }

                // Compound index on email + isActive
                if (!existingIndexNames.Contains("email_1_isActive_1"))
                {
                    var activeEmailIndex = new CreateIndexModel<User>(
                        Builders<User>.IndexKeys
                            .Ascending(u => u.Email)
                            .Ascending(u => u.IsActive),
                        new CreateIndexOptions { Name = "email_1_isActive_1" });
                    newIndexes.Add(activeEmailIndex);
                }

                if (newIndexes.Count > 0)
                {
                    _users.Indexes.CreateMany(newIndexes);
                }
            }
            catch (MongoCommandException ex) when (ex.CodeName == "IndexOptionsConflict" ||
                                                   ex.CodeName == "IndexKeySpecsConflict" ||
                                                   ex.Message.Contains("existing index has the same name"))
            {
                // Index already exists with same definition; safe to ignore
                // This handles the case where indexes were created by init scripts
            }
            catch (Exception ex)
            {
                // Log the error but don't fail the application startup
                // The indexes might already exist from the MongoDB init script
                Console.WriteLine($"Warning: Could not create indexes: {ex.Message}");
            }
        }

        // Create operations
        public async Task<User> CreateUserAsync(User user)
        {
            user.Id = ObjectId.GenerateNewId().ToString();
            user.CreatedAt = DateTime.UtcNow;
            user.UpdatedAt = DateTime.UtcNow;
            
            await _users.InsertOneAsync(user);
            return user;
        }

        // Read operations
        public async Task<User?> GetUserByIdAsync(string id)
        {
            return await _users.Find(u => u.Id == id).FirstOrDefaultAsync();
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _users.Find(u => u.Email == email && u.IsActive).FirstOrDefaultAsync();
        }

        public async Task<User?> GetUserByGoogleIdAsync(string googleId)
        {
            return await _users.Find(u => u.GoogleId == googleId && u.IsActive).FirstOrDefaultAsync();
        }

        public async Task<IEnumerable<User>> GetAllUsersAsync()
        {
            return await _users.Find(u => u.IsActive).ToListAsync();
        }

        public async Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role)
        {
            return await _users.Find(u => u.Role == role && u.IsActive).ToListAsync();
        }

        public async Task<bool> EmailExistsAsync(string email)
        {
            var count = await _users.CountDocumentsAsync(u => u.Email == email);
            return count > 0;
        }

        // Update operations
        public async Task<bool> UpdateUserAsync(string id, User user)
        {
            user.UpdatedAt = DateTime.UtcNow;
            var result = await _users.ReplaceOneAsync(u => u.Id == id, user);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> UpdateLastLoginAsync(string id)
        {
            var update = Builders<User>.Update.Set(u => u.LastLoginAt, DateTime.UtcNow)
                                              .Set(u => u.UpdatedAt, DateTime.UtcNow);
            var result = await _users.UpdateOneAsync(u => u.Id == id, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> VerifyEmailAsync(string id)
        {
            var update = Builders<User>.Update.Set(u => u.EmailVerified, true)
                                              .Set(u => u.UpdatedAt, DateTime.UtcNow);
            var result = await _users.UpdateOneAsync(u => u.Id == id, update);
            return result.ModifiedCount > 0;
        }

        public async Task<bool> ChangePasswordAsync(string id, string hashedPassword)
        {
            var update = Builders<User>.Update.Set(u => u.HashedPassword, hashedPassword)
                                              .Set(u => u.UpdatedAt, DateTime.UtcNow);
            var result = await _users.UpdateOneAsync(u => u.Id == id, update);
            return result.ModifiedCount > 0;
        }

        // Delete operations
        public async Task<bool> DeleteUserAsync(string id)
        {
            var result = await _users.DeleteOneAsync(u => u.Id == id);
            return result.DeletedCount > 0;
        }

        public async Task<bool> DeactivateUserAsync(string id)
        {
            var update = Builders<User>.Update.Set(u => u.IsActive, false)
                                              .Set(u => u.UpdatedAt, DateTime.UtcNow);
            var result = await _users.UpdateOneAsync(u => u.Id == id, update);
            return result.ModifiedCount > 0;
        }

        // Authentication helpers
        public async Task<User?> ValidateUserCredentialsAsync(string email, string hashedPassword)
        {
            return await _users.Find(u => u.Email == email && 
                                         u.HashedPassword == hashedPassword && 
                                         u.IsActive && 
                                         u.EmailVerified).FirstOrDefaultAsync();
        }
    }
} 