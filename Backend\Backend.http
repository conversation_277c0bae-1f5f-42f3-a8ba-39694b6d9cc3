@Backend_HostAddress = http://localhost:5000

### Test Register Endpoint
POST {{Backend_HostAddress}}/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Test123!",
  "firstName": "Test",
  "lastName": "User"
}

###

### Test Login Endpoint
POST {{Backend_HostAddress}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "Test123!"
}

###

### Test Profile Endpoint (Needs JWT token)
GET {{Backend_HostAddress}}/api/auth/profile
Authorization: Bearer YOUR_JWT_TOKEN_HERE

###

### Test Register with Existing Email (Should fail)
POST {{Backend_HostAddress}}/api/auth/register
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "AnotherPassword123!",
  "firstName": "Another",
  "lastName": "User"
}

###

### Test Login with Wrong Password (Should fail)
POST {{Backend_HostAddress}}/api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "WrongPassword"
}

###

### Test Health Check
GET {{Backend_HostAddress}}/swagger
Accept: application/json

###
