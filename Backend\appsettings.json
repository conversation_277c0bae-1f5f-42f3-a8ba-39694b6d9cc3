{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"MongoDB": "**************************************************************************", "Redis": "redis:6379"}, "DatabaseSettings": {"ConnectionString": "**************************************************************************", "DatabaseName": "niooai_database", "UsersCollectionName": "users", "MagazinesCollectionName": "magazines", "ChatSessionsCollectionName": "chat_sessions"}, "OpenAI": {"ApiKey": "", "Model": "gpt-4o", "EmbeddingModel": "text-embedding-3-small"}, "Qdrant": {"Url": "http://localhost:6333", "CollectionName": "magazine_embeddings"}, "Authentication": {"Google": {"ClientId": "336383118384-8h3vatv3ebbap3r3djfll3kq0lgn8qjd.apps.googleusercontent.com", "ClientSecret": "GOCSPX-yVoCZGiC1HBjuy6ix3ccD3rg63V"}, "Jwt": {"SecretKey": "your-super-secret-jwt-key-here-change-in-production", "Issuer": "NiooAI", "Audience": "NiooAI-Users", "ExpirationHours": 24}}, "Cors": {"AllowedOrigins": ["http://localhost:3000", "http://localhost:3001"]}}