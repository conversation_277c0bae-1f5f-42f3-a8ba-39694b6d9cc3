// MongoDB initialization script for NiooAI database
// This script runs when MongoDB container is first created

// Switch to the application database
db = db.getSiblingDB('niooai_database');

// Create collections with validation schemas
db.createCollection('users', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['email', 'name', 'createdAt'],
      properties: {
        email: {
          bsonType: 'string',
          pattern: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
          description: 'Email must be a valid email address'
        },
        name: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 100,
          description: 'Name is required and must be between 1 and 100 characters'
        },
        password: {
          bsonType: 'string',
          description: 'Hashed password for local authentication'
        },
        googleId: {
          bsonType: 'string',
          description: 'Google OAuth ID for Google authentication'
        },
        role: {
          bsonType: 'string',
          enum: ['user', 'admin'],
          description: 'User role'
        },
        isActive: {
          bsonType: 'bool',
          description: 'User active status'
        },
        createdAt: {
          bsonType: 'date',
          description: 'User creation timestamp'
        },
        updatedAt: {
          bsonType: 'date',
          description: 'Last update timestamp'
        }
      }
    }
  }
});

db.createCollection('magazines', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['title', 'uploadedBy', 'uploadedAt', 'status'],
      properties: {
        title: {
          bsonType: 'string',
          minLength: 1,
          maxLength: 200,
          description: 'Magazine title is required'
        },
        description: {
          bsonType: 'string',
          maxLength: 1000,
          description: 'Magazine description'
        },
        fileName: {
          bsonType: 'string',
          description: 'Original file name'
        },
        filePath: {
          bsonType: 'string',
          description: 'Path to the uploaded file'
        },
        fileType: {
          bsonType: 'string',
          enum: ['pdf', 'html', 'markdown'],
          description: 'Type of the uploaded file'
        },
        uploadedBy: {
          bsonType: 'objectId',
          description: 'User ID who uploaded the magazine'
        },
        uploadedAt: {
          bsonType: 'date',
          description: 'Upload timestamp'
        },
        processedAt: {
          bsonType: 'date',
          description: 'Processing completion timestamp'
        },
        status: {
          bsonType: 'string',
          enum: ['uploaded', 'processing', 'completed', 'failed'],
          description: 'Processing status of the magazine'
        },
        metadata: {
          bsonType: 'object',
          description: 'Additional metadata extracted from the file'
        }
      }
    }
  }
});

db.createCollection('chat_sessions', {
  validator: {
    $jsonSchema: {
      bsonType: 'object',
      required: ['userId', 'createdAt'],
      properties: {
        userId: {
          bsonType: 'objectId',
          description: 'User ID who owns this chat session'
        },
        magazineId: {
          bsonType: 'objectId',
          description: 'Magazine ID for context-specific chats'
        },
        title: {
          bsonType: 'string',
          maxLength: 200,
          description: 'Chat session title'
        },
        messages: {
          bsonType: 'array',
          items: {
            bsonType: 'object',
            required: ['role', 'content', 'timestamp'],
            properties: {
              role: {
                bsonType: 'string',
                enum: ['user', 'assistant'],
                description: 'Message sender role'
              },
              content: {
                bsonType: 'string',
                description: 'Message content'
              },
              timestamp: {
                bsonType: 'date',
                description: 'Message timestamp'
              },
              metadata: {
                bsonType: 'object',
                description: 'Additional message metadata'
              }
            }
          }
        },
        createdAt: {
          bsonType: 'date',
          description: 'Session creation timestamp'
        },
        updatedAt: {
          bsonType: 'date',
          description: 'Last update timestamp'
        }
      }
    }
  }
});

// Create indexes for better performance
db.users.createIndex({ 'email': 1 }, { unique: true });
db.users.createIndex({ 'googleId': 1 }, { sparse: true });
db.users.createIndex({ 'createdAt': 1 });

db.magazines.createIndex({ 'uploadedBy': 1 });
db.magazines.createIndex({ 'uploadedAt': 1 });
db.magazines.createIndex({ 'status': 1 });
db.magazines.createIndex({ 'title': 'text', 'description': 'text' });

db.chat_sessions.createIndex({ 'userId': 1 });
db.chat_sessions.createIndex({ 'magazineId': 1 }, { sparse: true });
db.chat_sessions.createIndex({ 'createdAt': 1 });

// Create a default admin user (optional)
db.users.insertOne({
  email: '<EMAIL>',
  name: 'Admin User',
  role: 'admin',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date()
});

print('NiooAI database initialized successfully with collections and indexes'); 