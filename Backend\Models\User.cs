using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System.ComponentModel.DataAnnotations;

namespace Backend.Models
{
    public class User
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string? Id { get; set; }

        [BsonElement("email")]
        [Required]
        [EmailAddress]
        public string Email { get; set; } = string.Empty;

        [BsonElement("hashedPassword")]
        [Required]
        public string HashedPassword { get; set; } = string.Empty;

        [BsonElement("firstName")]
        public string? FirstName { get; set; }

        [BsonElement("lastName")]
        public string? LastName { get; set; }

        [BsonElement("role")]
        public UserRole Role { get; set; } = UserRole.User;

        [BsonElement("isActive")]
        public bool IsActive { get; set; } = true;

        [BsonElement("emailVerified")]
        public bool EmailVerified { get; set; } = false;

        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("updatedAt")]
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        [BsonElement("lastLoginAt")]
        public DateTime? LastLoginAt { get; set; }

        // Google OAuth fields
        [BsonElement("googleId")]
        public string? GoogleId { get; set; }

        [BsonElement("profilePictureUrl")]
        public string? ProfilePictureUrl { get; set; }
    }

    public enum UserRole
    {
        User = 0,
        Admin = 1,
        SuperAdmin = 2
    }
} 